#!/usr/bin/env python3
"""
Start Vue-ECharts WebSocket server with proper eventlet configuration.
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Import after path setup
from user_scripts_restructured.visualization.vue_echarts.websocket_app import WebSocketChartServer
from user_scripts_restructured.visualization.vue_echarts.config import ChartConfig

def main():
    print("🚀 STARTING VUE-ECHARTS SERVER WITH EVENTLET")
    print("=" * 60)
    
    # Create server configuration
    config = ChartConfig(
        host='0.0.0.0',
        port=8082,
        debug=True,
        enable_websocket=True
    )
    
    print(f"🌐 Server URL: http://localhost:{config.port}")
    print(f"📊 Chart URL: http://localhost:{config.port}/chart/MNQ.CME")
    print(f"🔍 Diagnostic URL: http://localhost:{config.port}/diagnostic")
    print(f"📁 Catalog: {config.catalog_path}")
    print(f"🔧 WebSocket: {config.enable_websocket}")
    
    try:
        # Create the server
        server = WebSocketChartServer(config)
        print(f"\n✅ Vue-ECharts server initialized successfully!")
        print(f"🔧 Using eventlet for WebSocket support")
        
        # Start the server with proper eventlet configuration
        print(f"🔧 eventlet installed and ready")
        server.start_server()
        
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()