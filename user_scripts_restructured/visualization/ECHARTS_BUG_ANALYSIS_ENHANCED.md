     Issues Identified

     1. Store Instruments Loading: The Pinia store is not loading the instruments list, causing "No instruments available!"
     2. WebSocket Subscription: Connected but can't subscribe due to missing instrument in store
     3. Memory Pressure: Critical memory usage (90-95%) indicating potential memory leak

     Phase 1: Fix Store Initialization (High Priority)

     1. Check the echartsStore.ts initialize() method to ensure it loads instruments
     2. Add debugging to the store initialization to track where it fails
     3. Verify the /api/instruments endpoint is working and returning data
     4. Fix any API client issues preventing instrument loading

     Phase 2: Fix WebSocket Integration (Medium Priority)

     1. Ensure store sets currentInstrument properly after initialization
     2. Add automatic WebSocket subscription after store loads instruments
     3. Test WebSocket data flow from connection to chart updates

     Phase 3: Memory Optimization (Medium Priority)

     1. Investigate the memory pressure warnings (90-95% usage)
     2. Check for memory leaks in ECharts instance management
     3. Optimize data caching and cleanup routines
     4. Test with reduced dataset size to isolate memory issues

     Expected Outcome

     - Instruments load properly in dropdown
     - Chart renders with actual OHLC data instead of "Loading..."
     - WebSocket subscription works for real-time updates
     - Memory usage stabilizes under 70%

     Success Criteria

     - Console shows "Available instruments: [...]" with actual data
     - Chart displays candlestick visualization
     - WebSocket subscribes to instrument automatically
     - No critical memory warnings