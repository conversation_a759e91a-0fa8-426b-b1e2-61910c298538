/**
 * Enhanced ECharts API Composable with Vue.js Reactive Data Fetching
 * 
 * Implements Vue.js watchEffect patterns for automatic data re-fetching
 * following Context7 research validation of Vue.js + ECharts on-demand loading.
 * 
 * Key Features:
 * - watchEffect for reactive data fetching (Vue.js best practice)
 * - ECharts large mode optimization for 720K+ datasets
 * - Direct PyArrow-to-JSON pipeline with minimal overhead
 * - Smart cache as intelligent fallback
 */

import { ref, computed, watchEffect, reactive, toRefs } from 'vue';
import { useEChartsStore } from '../stores/echartsStore';

interface LoadChartDataOptions {
  limit?: number;
  sampling?: string;
  before_timestamp?: string;
  after_timestamp?: string;
  max_points?: number;
}

interface InfiniteScrollLoadOptions {
  instrument: string;
  timeframe: string;
  direction: 'left' | 'right';
  count: number;
  sampling: string;
  before_timestamp?: string;
  after_timestamp?: string;
}

// Enhanced reactive state following Vue.js patterns from Context7 research
interface ReactiveDataState {
  isLoading: boolean;
  lastError: string | null;
  currentData: any[];
  lastFetchTime: number;
  largeDatasetMode: boolean;
  fetchTrigger: number; // For manual refresh
}

export function useEChartsAPI() {
  const store = useEChartsStore();
  
  // Enhanced reactive state using Vue.js reactive() pattern
  const state = reactive<ReactiveDataState>({
    isLoading: false,
    lastError: null,
    currentData: [],
    lastFetchTime: 0,
    largeDatasetMode: false,
    fetchTrigger: 0
  });
  
  // Cache
  const cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  const defaultTTL = 5 * 60 * 1000; // 5 minutes
  
  // Base API configuration
  const baseURL = '';
  const apiTimeout = 10000;
  
  // API client with error handling
  const apiClient = async (endpoint: string, options: RequestInit = {}) => {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), apiTimeout);
    
    try {
      const response = await fetch(`${baseURL}${endpoint}`, {
        ...options,
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        }
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        throw new Error(`API Error: ${response.status} ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error: any) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new Error('Request timeout');
      }
      
      throw error;
    }
  };
  
  // Cache utilities
  const getCacheKey = (endpoint: string, params: any) => {
    const paramString = JSON.stringify(params, Object.keys(params).sort());
    return `${endpoint}:${paramString}`;
  };
  
  const getFromCache = (key: string) => {
    const cached = cache.get(key);
    if (!cached) return null;
    
    const isExpired = Date.now() - cached.timestamp > cached.ttl;
    if (isExpired) {
      cache.delete(key);
      return null;
    }
    
    return cached.data;
  };
  
  const setCache = (key: string, data: any, ttl: number = defaultTTL) => {
    cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  };
  
  const clearCache = () => {
    cache.clear();
  };

  // ==========================================
  // PHASE 2.3: Vue.js Reactive Data Fetching
  // ==========================================
  
  /**
   * Enhanced reactive data fetcher using Vue.js watchEffect pattern
   * Based on Context7 research of Vue.js Core documentation patterns
   * 
   * Automatically re-fetches data when dependencies change, following
   * the Vue.js examples like GitHub commits fetching with watchEffect
   */
  const useReactiveDataFetching = (instrumentRef: any, timeframeRef: any, optionsRef: any = ref({})) => {
    // Reactive computed values for dependencies
    const currentInstrument = computed(() => instrumentRef.value);
    const currentTimeframe = computed(() => timeframeRef.value);
    const currentOptions = computed(() => optionsRef.value);
    
    // Enhanced data pipeline state
    const pipelineState = reactive({
      useDirectPyArrow: true,
      smartCacheFallback: true,
      echartsLargeMode: false,
      dataPoints: 0,
      memoryUsage: 0
    });
    
    // Reactive data fetching with watchEffect (Vue.js pattern from Context7)
    watchEffect(async () => {
      // Skip if no instrument selected
      if (!currentInstrument.value || !currentTimeframe.value) {
        state.currentData = [];
        return;
      }
      
      // Track dependency changes for debugging
      console.log('🔄 Vue.js watchEffect triggered:', {
        instrument: currentInstrument.value,
        timeframe: currentTimeframe.value,
        options: currentOptions.value,
        trigger: state.fetchTrigger
      });
      
      try {
        state.isLoading = true;
        state.lastError = null;
        
        const startTime = performance.now();
        
        // Phase 2.4: Direct PyArrow-to-JSON pipeline
        const data = await loadDataWithDirectPyArrow(
          currentInstrument.value,
          currentTimeframe.value,
          currentOptions.value
        );
        
        const loadTime = performance.now() - startTime;
        state.lastFetchTime = loadTime;
        
        // Phase 2.5: Enable ECharts large mode for 720K+ datasets
        if (data.ohlc && data.ohlc.length > 2000) {
          pipelineState.echartsLargeMode = true;
          state.largeDatasetMode = true;
          console.log('📊 ECharts large mode enabled for dataset:', data.ohlc.length);
        }
        
        // Update reactive state
        state.currentData = data.ohlc || [];
        pipelineState.dataPoints = state.currentData.length;
        
        // Record performance metrics
        store.recordLoadTime(loadTime);
        
        console.log('✅ Vue.js reactive data fetch completed:', {
          bars: state.currentData.length,
          loadTime: Math.round(loadTime),
          largeMode: state.largeDatasetMode,
          memoryUsage: pipelineState.memoryUsage
        });
        
      } catch (error: any) {
        console.error('❌ Vue.js reactive data fetch failed:', error);
        state.lastError = error.message;
        
        // Phase 2.7: Smart cache fallback when direct loading fails
        if (pipelineState.smartCacheFallback) {
          const originalError = error.message; // Fix: Preserve original error context
          try {
            console.log('🔄 Attempting smart cache fallback...');
            const fallbackData = await loadDataWithSmartCacheFallback(
              currentInstrument.value,
              currentTimeframe.value,
              currentOptions.value
            );
            
            state.currentData = fallbackData.ohlc || [];
            // Fix: Preserve original error context even when fallback succeeds for debugging
            state.lastError = `Direct PyArrow pipeline failed (${originalError}), recovered via smart cache fallback`;
            
            console.log('✅ Smart cache fallback successful:', state.currentData.length, 'Original error:', originalError);
          } catch (fallbackError: any) {
            console.error('💥 Smart cache fallback also failed:', fallbackError);
            // Fix: Enhanced error message with full context
            state.lastError = `Direct loading failed: ${originalError}. Fallback failed: ${fallbackError.message}`;
          }
        }
      } finally {
        state.isLoading = false;
      }
    });
    
    // Manual refresh trigger (Vue.js pattern for external updates)
    const refreshData = () => {
      state.fetchTrigger++;
    };
    
    return {
      ...toRefs(state),
      ...toRefs(pipelineState),
      refreshData
    };
  };

  /**
   * Phase 2.4: Direct PyArrow-to-JSON pipeline
   * Implements efficient data loading matching ECharts async patterns from Context7
   */
  const loadDataWithDirectPyArrow = async (
    instrument: string,
    timeframe: string,
    options: LoadChartDataOptions = {}
  ) => {
    // Build optimized request for PyArrow pipeline
    const params = new URLSearchParams({
      timeframe,
      limit: String(options.limit || 100),
      sampling: options.sampling || 'none',
      format: 'echarts',  // Request ECharts-optimized format
      pipeline: 'direct_pyarrow'  // Use direct PyArrow-to-JSON
    });
    
    const endpoint = `/api/chart-data/${instrument}?${params}`;
    console.log('🚀 Direct PyArrow pipeline request:', endpoint);
    
    return await apiClient(endpoint);
  };

  /**
   * Phase 2.7: Smart cache fallback with automatic detection
   * Provides reliability while optimizing primary path
   */
  const loadDataWithSmartCacheFallback = async (
    instrument: string,
    timeframe: string,
    options: LoadChartDataOptions = {}
  ) => {
    const params = new URLSearchParams({
      timeframe,
      limit: String(options.limit || 100),
      sampling: options.sampling || 'adaptive',
      format: 'echarts',
      pipeline: 'smart_cache_fallback'
    });
    
    const endpoint = `/api/chart-data/${instrument}?${params}`;
    console.log('🔄 Smart cache fallback request:', endpoint);
    
    return await apiClient(endpoint);
  };
  
  // Main API methods (existing)
  const loadChartData = async (
    instrument: string, 
    timeframe: string, 
    options: LoadChartDataOptions = {}
  ) => {
    const cacheKey = getCacheKey('/api/chart-data', {
      instrument,
      timeframe,
      ...options
    });
    
    // Check cache first
    const cached = getFromCache(cacheKey);
    if (cached) {
      console.log('Returning cached chart data');
      return cached;
    }
    
    try {
      state.isLoading = true;
      state.lastError = null;
      
      // Use the standard chart-data endpoint which works reliably
      const params = new URLSearchParams({
        timeframe,
        limit: String(options.limit || 100)
      });
      
      const endpoint = `/api/chart-data/${instrument}?${params}`;
      const startTime = performance.now();
      
      const data = await apiClient(endpoint);
      
      const endTime = performance.now();
      const loadTime = endTime - startTime;
      
      // Record performance
      store.recordLoadTime(loadTime);
      
      // Cache the result
      setCache(cacheKey, data);
      
      console.log(`Loaded chart data for ${instrument}:`, {
        bars: data.ohlc?.length || data.bars?.length || 0,
        loadTime: Math.round(loadTime),
        cached: false,
        endpoint,
        dataSource: data.data_source,
        barsReturned: data.bars_returned
      });
      
      return data;
    } catch (error: any) {
      state.lastError = error.message;
      store.setError(error.message);
      console.error('Failed to load chart data:', error);
      throw error;
    } finally {
      state.isLoading = false;
    }
  };
  
  const loadInfiniteScrollData = async (options: InfiniteScrollLoadOptions) => {
    try {
      state.isLoading = true;
      state.lastError = null;
      
      const {
        instrument,
        timeframe,
        direction,
        count,
        sampling,
        before_timestamp,
        after_timestamp
      } = options;
      
      // Build query parameters
      const params = new URLSearchParams({
        timeframe,
        max_points: String(count),
        sampling
      });
      
      if (before_timestamp) {
        params.append('before_timestamp_seconds', String(Math.floor(Date.parse(before_timestamp) / 1000)));
      }
      
      if (after_timestamp) {
        params.append('after_timestamp_seconds', String(Math.floor(Date.parse(after_timestamp) / 1000)));
      }
      
      const endpoint = `/api/echarts-data/${instrument}?${params}`;
      const startTime = performance.now();
      
      const data = await apiClient(endpoint);
      
      const endTime = performance.now();
      const loadTime = endTime - startTime;
      
      // Record performance
      store.recordLoadTime(loadTime);
      
      console.log(`Loaded infinite scroll data for ${instrument} (${direction}):`, {
        bars: data.bars?.length || 0,
        loadTime: Math.round(loadTime),
        direction
      });
      
      return {
        ...data,
        direction,
        has_more_historical: data.has_more_historical !== false,
        has_more_recent: data.has_more_recent !== false
      };
    } catch (error: any) {
      state.lastError = error.message;
      store.setError(error.message);
      console.error('Failed to load infinite scroll data:', error);
      throw error;
    } finally {
      state.isLoading = false;
    }
  };
  
  const loadInstruments = async () => {
    const cacheKey = getCacheKey('/api/instruments', {});
    
    // Check cache first
    const cached = getFromCache(cacheKey);
    if (cached) {
      return cached;
    }
    
    try {
      const data = await apiClient('/api/instruments');
      
      // Cache for longer since instruments don't change often
      setCache(cacheKey, data, 30 * 60 * 1000); // 30 minutes
      
      return data;
    } catch (error: any) {
      lastError.value = error.message;
      store.setError(error.message);
      throw error;
    }
  };
  
  const loadTimeframes = async () => {
    const cacheKey = getCacheKey('/api/timeframes', {});
    
    // Check cache first
    const cached = getFromCache(cacheKey);
    if (cached) {
      return cached;
    }
    
    try {
      const data = await apiClient('/api/timeframes');
      
      // Cache for a long time since timeframes rarely change
      setCache(cacheKey, data, 60 * 60 * 1000); // 1 hour
      
      return data;
    } catch (error: any) {
      lastError.value = error.message;
      store.setError(error.message);
      throw error;
    }
  };
  
  const checkHealth = async () => {
    try {
      const data = await apiClient('/api/health');
      return data;
    } catch (error: any) {
      lastError.value = error.message;
      throw error;
    }
  };
  
  // Batch loading for multiple instruments
  const loadMultipleInstruments = async (
    instruments: string[], 
    timeframe: string, 
    options: LoadChartDataOptions = {}
  ) => {
    const promises = instruments.map(instrument => 
      loadChartData(instrument, timeframe, options).catch(error => ({
        instrument,
        error: error.message
      }))
    );
    
    const results = await Promise.all(promises);
    
    const successful = results.filter(result => !('error' in result));
    const failed = results.filter(result => 'error' in result);
    
    if (failed.length > 0) {
      console.warn('Some instruments failed to load:', failed);
    }
    
    return {
      successful,
      failed,
      total: instruments.length
    };
  };
  
  // Preload data for better user experience
  const preloadData = async (instrument: string, timeframe: string) => {
    try {
      // Preload with smaller chunk size to avoid impacting performance
      await loadChartData(instrument, timeframe, {
        limit: 50,
        sampling: 'adaptive'
      });
    } catch (error) {
      // Silently fail for preloading
      console.warn('Preload failed:', error);
    }
  };
  
  // Smart retry with exponential backoff
  const retryWithBackoff = async <T>(
    fn: () => Promise<T>, 
    maxRetries: number = 3,
    baseDelay: number = 1000
  ): Promise<T> => {
    let lastError: Error;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error: any) {
        lastError = error;
        
        if (attempt === maxRetries) {
          break;
        }
        
        const delay = baseDelay * Math.pow(2, attempt);
        console.log(`Retry attempt ${attempt + 1} after ${delay}ms`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError!;
  };
  
  return {
    // Enhanced reactive state
    state: toRefs(state),
    
    // Phase 2.3: Vue.js Reactive Data Fetching
    useReactiveDataFetching,
    
    // Phase 2.4: Direct PyArrow pipeline
    loadDataWithDirectPyArrow,
    
    // Phase 2.7: Smart cache fallback
    loadDataWithSmartCacheFallback,
    
    // Legacy methods (maintained for compatibility)
    loadChartData,
    loadInfiniteScrollData,
    loadInstruments,
    loadTimeframes,
    checkHealth,
    loadMultipleInstruments,
    preloadData,
    retryWithBackoff,
    
    // Cache management
    clearCache
  };
}