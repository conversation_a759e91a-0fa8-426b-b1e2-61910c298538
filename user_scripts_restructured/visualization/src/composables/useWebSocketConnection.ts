/**
 * WebSocket Connection Composable
 * 
 * Manages WebSocket connection lifecycle with automatic reconnection
 * and reactive state management.
 */

import { ref, computed, onUnmounted } from 'vue';
import { io, Socket } from 'socket.io-client';
import { useEChartsStore } from '../stores/echartsStore';

export function useWebSocketConnection() {
  const store = useEChartsStore();
  
  // State
  const socket = ref<Socket | null>(null);
  const connectionStatus = ref<'connected' | 'connecting' | 'disconnected'>('disconnected');
  const lastError = ref<string | null>(null);
  const reconnectTimer = ref<number | null>(null);
  
  // Computed
  const isConnected = computed(() => connectionStatus.value === 'connected');
  const isConnecting = computed(() => connectionStatus.value === 'connecting');
  
  // Connection configuration
  const socketConfig = {
    transports: ['websocket', 'polling'],
    timeout: 5000,
    reconnection: true,
    reconnectionAttempts: 5,
    reconnectionDelay: 1000,
    reconnectionDelayMax: 5000
  };
  
  // Connection methods
  const connect = () => {
    if (socket.value?.connected) {
      console.log('🔌 Socket already connected');
      return;
    }
    
    try {
      console.log('🔄 Initializing WebSocket connection...', {
        url: window.location.origin,
        config: socketConfig,
        timestamp: new Date().toISOString()
      });
      
      connectionStatus.value = 'connecting';
      store.setConnectionStatus('connecting');
      
      // Create socket connection
      socket.value = io(socketConfig);
      console.log('📡 Socket.IO client created', socket.value);
      
      // Event handlers
      socket.value.on('connect', onConnect);
      socket.value.on('disconnect', onDisconnect);
      socket.value.on('connect_error', onConnectError);
      socket.value.on('reconnect', onReconnect);
      socket.value.on('reconnect_error', onReconnectError);
      socket.value.on('reconnect_failed', onReconnectFailed);
      
      // Chart data events
      socket.value.on('chart_data', onChartData);
      socket.value.on('real_time_update', onRealTimeUpdate);
      socket.value.on('infinite_scroll_data', onInfiniteScrollData);
      
      // Status events
      socket.value.on('status_update', onStatusUpdate);
      socket.value.on('error', onError);
      
    } catch (error) {
      console.error('Failed to create socket connection:', error);
      connectionStatus.value = 'disconnected';
      store.setConnectionStatus('disconnected');
      lastError.value = 'Failed to initialize connection';
    }
  };
  
  const disconnect = () => {
    if (reconnectTimer.value) {
      clearTimeout(reconnectTimer.value);
      reconnectTimer.value = null;
    }
    
    if (socket.value) {
      socket.value.disconnect();
      socket.value = null;
    }
    
    connectionStatus.value = 'disconnected';
    store.setConnectionStatus('disconnected');
  };
  
  const reconnect = () => {
    disconnect();
    
    if (store.canReconnect) {
      store.incrementReconnectAttempts();
      
      reconnectTimer.value = window.setTimeout(() => {
        connect();
      }, Math.min(1000 * Math.pow(2, store.wsReconnectAttempts), 30000));
    } else {
      console.error('Max reconnection attempts reached');
      lastError.value = 'Max reconnection attempts reached';
    }
  };
  
  // Event handlers with enhanced debugging
  const onConnect = () => {
    console.log('✅ WebSocket connected successfully!', {
      socketId: socket.value?.id,
      transport: socket.value?.io.engine.transport.name,
      timestamp: new Date().toISOString()
    });
    
    connectionStatus.value = 'connected';
    store.setConnectionStatus('connected');
    store.resetReconnectAttempts();
    lastError.value = null;
    
    // Subscribe to chart data for current instrument
    if (store.currentInstrument) {
      console.log('🔔 Auto-subscribing to instrument:', {
        instrument: store.currentInstrument,
        timeframe: store.currentTimeframe
      });
      subscribeToInstrument(store.currentInstrument, store.currentTimeframe);
    } else {
      console.warn('⚠️ No current instrument to subscribe to');
    }
  };
  
  const onDisconnect = (reason: string) => {
    console.warn('🔌 WebSocket disconnected:', {
      reason,
      socketId: socket.value?.id,
      timestamp: new Date().toISOString(),
      wasConnected: socket.value?.connected
    });
    
    connectionStatus.value = 'disconnected';
    store.setConnectionStatus('disconnected');
    
    // Attempt reconnection for unexpected disconnections
    if (reason === 'io server disconnect') {
      // Server initiated disconnect, don't reconnect automatically
      console.warn('🚫 Server initiated disconnect - no auto-reconnect');
      lastError.value = 'Server disconnected';
    } else {
      // Client-side disconnect or network issues, attempt reconnect
      console.log('🔄 Attempting automatic reconnection...');
      reconnect();
    }
  };
  
  const onConnectError = (error: any) => {
    console.error('💥 WebSocket connection error:', {
      error: error.message || error,
      type: error.type,
      description: error.description,
      timestamp: new Date().toISOString(),
      attemptNumber: store.wsReconnectAttempts
    });
    
    connectionStatus.value = 'disconnected';
    store.setConnectionStatus('disconnected');
    lastError.value = error.message || 'Connection failed';
    
    // Attempt reconnection
    reconnect();
  };
  
  const onReconnect = (attemptNumber: number) => {
    console.log('Socket reconnected after', attemptNumber, 'attempts');
    connectionStatus.value = 'connected';
    store.setConnectionStatus('connected');
    store.resetReconnectAttempts();
    lastError.value = null;
  };
  
  const onReconnectError = (error: any) => {
    console.error('Socket reconnection error:', error);
    lastError.value = 'Reconnection failed';
  };
  
  const onReconnectFailed = () => {
    console.error('Socket reconnection failed after max attempts');
    connectionStatus.value = 'disconnected';
    store.setConnectionStatus('disconnected');
    lastError.value = 'Reconnection failed - max attempts reached';
  };
  
  // Data event handlers
  const onChartData = (data: any) => {
    console.log('Received chart data:', data);
    store.updateChartData(data.bars || []);
  };
  
  const onRealTimeUpdate = (data: any) => {
    console.log('Received real-time update:', data);
    
    if (data.bar) {
      // Update or append the latest bar
      const currentData = store.chartData;
      const lastBar = currentData[currentData.length - 1];
      
      if (lastBar && lastBar.time === data.bar.time) {
        // Update existing bar
        currentData[currentData.length - 1] = data.bar;
      } else {
        // Append new bar
        store.appendChartData([data.bar], 'right');
      }
    }
  };
  
  const onInfiniteScrollData = (data: any) => {
    console.log('Received infinite scroll data:', data);
    
    if (data.bars && data.bars.length > 0) {
      store.appendChartData(data.bars, data.direction || 'right');
      
      // Update infinite scroll state
      store.setInfiniteScrollState({
        hasMoreHistorical: data.has_more_historical,
        hasMoreRecent: data.has_more_recent
      });
    }
  };
  
  const onStatusUpdate = (data: any) => {
    console.log('Received status update:', data);
    
    if (data.performance) {
      store.updatePerformanceMetrics(data.performance);
    }
  };
  
  const onError = (error: any) => {
    console.error('Socket error:', error);
    lastError.value = error.message || 'Socket error';
    store.setError(error.message);
  };
  
  // API methods
  const subscribeToInstrument = (instrument: string, timeframe: string) => {
    if (!socket.value?.connected) {
      console.warn('Cannot subscribe: socket not connected');
      return;
    }
    
    socket.value.emit('subscribe_chart', {
      instrument,
      timeframe,
      enable_real_time: true
    });
  };
  
  const unsubscribeFromInstrument = (instrument: string) => {
    if (!socket.value?.connected) {
      return;
    }
    
    socket.value.emit('unsubscribe_chart', {
      instrument
    });
  };
  
  const requestChartData = (instrument: string, timeframe: string, options: any = {}) => {
    if (!socket.value?.connected) {
      console.warn('Cannot request data: socket not connected');
      return;
    }
    
    socket.value.emit('request_chart_data', {
      instrument,
      timeframe,
      ...options
    });
  };
  
  const requestInfiniteScrollData = (options: any) => {
    if (!socket.value?.connected) {
      console.warn('Cannot request infinite scroll data: socket not connected');
      return;
    }
    
    socket.value.emit('request_infinite_scroll', options);
  };
  
  // Cleanup
  onUnmounted(() => {
    disconnect();
  });
  
  return {
    // State
    connectionStatus,
    lastError,
    
    // Computed
    isConnected,
    isConnecting,
    
    // Methods
    connect,
    disconnect,
    reconnect,
    subscribeToInstrument,
    unsubscribeFromInstrument,
    requestChartData,
    requestInfiniteScrollData
  };
}