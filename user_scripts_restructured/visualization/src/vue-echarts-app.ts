/**
 * Vue.js + ECharts Application Entry Point
 * 
 * Main entry point for the Vue.js-based ECharts visualization system,
 * inspired by FreqUI architecture with reactive data management.
 */

import { createApp } from 'vue';
import { createPinia } from 'pinia';
import VChart from 'vue-echarts';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { CandlestickChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
} from 'echarts/components';
import EChartsApp from './components/EChartsApp.vue';

// Register ECharts components globally
use([
  CanvasRenderer,
  CandlestickChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
]);

// Create Vue application
console.log('🚀 Creating Vue.js application...');
const app = createApp(EChartsApp);

// Register Vue-ECharts component globally
console.log('📊 Registering Vue-ECharts component globally...');
app.component('VChart', VChart);

// Add Pinia store for state management
console.log('🗄️ Setting up Pinia store...');
const pinia = createPinia();
app.use(pinia);

// Mount the application
console.log('⚡ Mounting Vue.js application to #app...');
try {
  app.mount('#app');
  console.log('✅ Vue.js application mounted successfully!');
  console.log('📊 Vue-ECharts component registered as VChart');
  console.log('🏪 Pinia store initialized');
} catch (error) {
  console.error('💥 Failed to mount Vue.js application:', error);
  throw error;
}

// Export for testing and debugging
export { app, pinia };