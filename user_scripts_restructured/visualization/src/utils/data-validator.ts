/**
 * Data Validation System with Zod
 * 
 * Comprehensive runtime schema validation for OHLC data integrity as recommended
 * in the bug analysis. Prevents silent data corruption and provides detailed
 * error reporting with context.
 */

import { z } from 'zod';

// Custom error class for chart data validation
export class ChartDataError extends Error {
  constructor(
    message: string,
    public readonly data?: any,
    public readonly validationErrors?: z.ZodIssue[]
  ) {
    super(message);
    this.name = 'ChartDataError';
  }
}

// Base schemas for financial data validation
const TimestampSchema = z.union([
  z.number().positive('Timestamp must be positive'),
  z.string().transform((val, ctx) => {
    // Try to parse as ISO string first
    let timestamp: number;
    
    if (val.includes('T') || val.includes('-')) {
      // ISO string format
      timestamp = Date.parse(val);
    } else {
      // Assume Unix timestamp string
      timestamp = parseFloat(val) * (val.length <= 10 ? 1000 : 1);
    }
    
    if (isNaN(timestamp) || timestamp <= 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: `Invalid timestamp: ${val}`,
      });
      return z.NEVER;
    }
    
    return timestamp;
  })
]).describe('Unix timestamp in milliseconds or ISO string');

const PriceSchema = z.union([
  z.number().positive('Price must be positive'),
  z.string().transform((val, ctx) => {
    const price = parseFloat(val);
    if (isNaN(price) || price <= 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: `Invalid price: ${val}`,
      });
      return z.NEVER;
    }
    return price;
  })
]).describe('Positive price value');

const VolumeSchema = z.union([
  z.number().min(0, 'Volume cannot be negative'),
  z.string().transform((val, ctx) => {
    const volume = parseFloat(val);
    if (isNaN(volume) || volume < 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: `Invalid volume: ${val}`,
      });
      return z.NEVER;
    }
    return volume;
  })
]).describe('Non-negative volume value');

// OHLC Bar Schema with comprehensive validation
export const OHLCBarSchema = z.object({
  time: TimestampSchema,
  open: PriceSchema,
  high: PriceSchema,
  low: PriceSchema,
  close: PriceSchema,
  volume: VolumeSchema.optional()
}).refine((data) => {
  // Validate OHLC relationships
  if (data.high < data.low) {
    return false;
  }
  if (data.high < data.open || data.high < data.close) {
    return false;
  }
  if (data.low > data.open || data.low > data.close) {
    return false;
  }
  return true;
}, {
  message: "Invalid OHLC relationships: high must be >= low, open, and close; low must be <= open and close"
});

// Chart Data Schema (array of OHLC bars)
export const ChartDataSchema = z.array(OHLCBarSchema);

// Incremental Data Schema with continuity validation
export const IncrementalDataSchema = z.object({
  bars: ChartDataSchema,
  hasMore: z.boolean(),
  direction: z.enum(['left', 'right']),
  totalBars: z.number().min(0),
  metadata: z.object({
    instrument: z.string(),
    timeframe: z.string(),
    loadTime: z.number().optional(),
    cacheHit: z.boolean().optional()
  }).optional()
});

// WebSocket Message Schema
export const WebSocketMessageSchema = z.object({
  type: z.enum(['chart_data', 'chart_update', 'error', 'connection_status']),
  payload: z.any(),
  timestamp: z.number(),
  requestId: z.string().optional()
});

// Import official data quality schema
import type { BaseDataQuality, EnhancedDataQuality } from '../types/data-quality';

// API Response Schema using official data quality interface
export const APIResponseSchema = z.object({
  ohlc: ChartDataSchema,
  bars_returned: z.number().min(0),
  cache_hit: z.boolean().optional(),
  load_time_ms: z.number().optional(),
  data_quality: z.object({
    is_valid: z.boolean(),
    issues: z.array(z.string()).nullable().optional(),
    warnings: z.array(z.string()).nullable().optional(),
    data_source: z.string().optional(),
    // Legacy compatibility fields (mapped to new structure)
    valid_bars: z.number().optional(),
    invalid_bars: z.number().optional(),
    duplicate_bars: z.number().optional()
  }).optional()
});

// Types derived from schemas
export type OHLCData = z.infer<typeof OHLCBarSchema>;
export type ChartData = z.infer<typeof ChartDataSchema>;
export type IncrementalData = z.infer<typeof IncrementalDataSchema>;
export type WebSocketMessage = z.infer<typeof WebSocketMessageSchema>;
export type APIResponse = z.infer<typeof APIResponseSchema>;

/**
 * Data Validation Class with comprehensive error handling
 */
export class DataValidator {
  private static validationMetrics = {
    totalValidations: 0,
    successfulValidations: 0,
    failedValidations: 0,
    commonErrors: new Map<string, number>()
  };
  
  /**
   * Validate chart data with detailed error reporting
   */
  static validateChartData(data: unknown): ChartData {
    this.validationMetrics.totalValidations++;
    
    try {
      const validated = ChartDataSchema.parse(data);
      this.validationMetrics.successfulValidations++;
      
      console.log(`✅ Validated ${validated.length} OHLC bars successfully`);
      return validated;
      
    } catch (error) {
      this.validationMetrics.failedValidations++;
      
      if (error instanceof z.ZodError) {
        const errorMessage = this.formatZodError(error);
        this.recordCommonError(errorMessage);
        
        throw new ChartDataError(
          `Chart data validation failed: ${errorMessage}`,
          data,
          error.issues
        );
      } else {
        const errorMessage = `Unexpected validation error: ${error.message}`;
        this.recordCommonError(errorMessage);
        throw new ChartDataError(errorMessage, data);
      }
    }
  }
  
  /**
   * Validate incremental data with continuity checks
   */
  static validateIncrementalData(newData: unknown, existingData: ChartData): IncrementalData {
    this.validationMetrics.totalValidations++;
    
    try {
      const validated = IncrementalDataSchema.parse(newData);
      this.validationMetrics.successfulValidations++;
      
      // Check for timestamp continuity
      if (existingData.length > 0 && validated.bars.length > 0) {
        this.validateTimestampContinuity(validated, existingData);
      }
      
      // Check for overlapping data
      const overlaps = this.detectOverlappingData(validated.bars, existingData);
      if (overlaps.length > 0) {
        console.warn(`⚠️ Detected ${overlaps.length} overlapping bars in incremental data`);
      }
      
      console.log(`✅ Validated incremental data: ${validated.bars.length} bars, direction: ${validated.direction}`);
      return validated;
      
    } catch (error) {
      this.validationMetrics.failedValidations++;
      
      if (error instanceof z.ZodError) {
        const errorMessage = this.formatZodError(error);
        this.recordCommonError(errorMessage);
        
        throw new ChartDataError(
          `Incremental data validation failed: ${errorMessage}`,
          newData,
          error.issues
        );
      } else {
        throw error;
      }
    }
  }
  
  /**
   * Validate timestamp continuity for incremental data
   */
  private static validateTimestampContinuity(validated: IncrementalData, existingData: ChartData): void {
    const { bars, direction } = validated;
    
    if (direction === 'right') {
      // Recent data - should come after existing data
      const lastExisting = existingData[existingData.length - 1];
      const firstNew = bars[0];
      
      if (firstNew.time <= lastExisting.time) {
        throw new ChartDataError(
          `Timestamp continuity violation: New data timestamp (${firstNew.time}) must be after last existing timestamp (${lastExisting.time})`
        );
      }
    } else {
      // Historical data - should come before existing data
      const firstExisting = existingData[0];
      const lastNew = bars[bars.length - 1];
      
      if (lastNew.time >= firstExisting.time) {
        throw new ChartDataError(
          `Timestamp continuity violation: Historical data timestamp (${lastNew.time}) must be before first existing timestamp (${firstExisting.time})`
        );
      }
    }
  }
  
  /**
   * Detect overlapping data between new and existing bars
   */
  private static detectOverlappingData(newBars: ChartData, existingBars: ChartData): ChartData {
    const existingTimes = new Set(existingBars.map(bar => bar.time));
    return newBars.filter(bar => existingTimes.has(bar.time));
  }
  
  /**
   * Validate WebSocket message structure
   */
  static validateWebSocketMessage(data: unknown): WebSocketMessage {
    this.validationMetrics.totalValidations++;
    
    try {
      const validated = WebSocketMessageSchema.parse(data);
      this.validationMetrics.successfulValidations++;
      return validated;
      
    } catch (error) {
      this.validationMetrics.failedValidations++;
      
      if (error instanceof z.ZodError) {
        const errorMessage = this.formatZodError(error);
        this.recordCommonError(errorMessage);
        
        throw new ChartDataError(
          `WebSocket message validation failed: ${errorMessage}`,
          data,
          error.issues
        );
      } else {
        throw error;
      }
    }
  }
  
  /**
   * Validate API response structure
   */
  static validateAPIResponse(data: unknown): APIResponse {
    this.validationMetrics.totalValidations++;

    // Debug logging to see what we're actually receiving
    console.log('🔍 DEBUG: Received API response data:', JSON.stringify(data, null, 2));

    try {
      const validated = APIResponseSchema.parse(data);
      this.validationMetrics.successfulValidations++;

      console.log(`✅ Validated API response: ${validated.bars_returned} bars`);
      return validated;

    } catch (error) {
      this.validationMetrics.failedValidations++;

      if (error instanceof z.ZodError) {
        const errorMessage = this.formatZodError(error);
        this.recordCommonError(errorMessage);

        // Additional debug logging for validation failures
        console.error('🔍 DEBUG: Validation failed for data:', JSON.stringify(data, null, 2));
        console.error('🔍 DEBUG: Zod error details:', error.issues);

        throw new ChartDataError(
          `API response validation failed: ${errorMessage}`,
          data,
          error.issues
        );
      } else {
        throw error;
      }
    }
  }
  
  /**
   * Sanitize and fix common data issues
   */
  static sanitizeOHLCData(data: any[]): ChartData {
    const sanitized: ChartData = [];
    
    for (const bar of data) {
      try {
        // Try to parse and fix common issues
        const sanitizedBar = {
          time: this.sanitizeTimestamp(bar.time),
          open: this.sanitizePrice(bar.open),
          high: this.sanitizePrice(bar.high),
          low: this.sanitizePrice(bar.low),
          close: this.sanitizePrice(bar.close),
          volume: bar.volume !== undefined ? this.sanitizeVolume(bar.volume) : undefined
        };
        
        // Validate OHLC relationships and fix if possible
        const fixed = this.fixOHLCRelationships(sanitizedBar);
        
        // Validate the fixed bar
        const validated = OHLCBarSchema.parse(fixed);
        sanitized.push(validated);
        
      } catch (error) {
        console.warn(`⚠️ Skipping invalid bar:`, bar, error.message);
        continue;
      }
    }
    
    return sanitized;
  }
  
  /**
   * Sanitize timestamp values
   */
  private static sanitizeTimestamp(value: any): number {
    if (typeof value === 'number') {
      // Convert seconds to milliseconds if needed
      return value < 1e10 ? value * 1000 : value;
    }
    
    if (typeof value === 'string') {
      if (value.includes('T') || value.includes('-')) {
        return Date.parse(value);
      } else {
        const num = parseFloat(value);
        return num < 1e10 ? num * 1000 : num;
      }
    }
    
    throw new Error(`Cannot sanitize timestamp: ${value}`);
  }
  
  /**
   * Sanitize price values
   */
  private static sanitizePrice(value: any): number {
    const price = typeof value === 'number' ? value : parseFloat(value);
    
    if (isNaN(price) || price <= 0) {
      throw new Error(`Invalid price: ${value}`);
    }
    
    return price;
  }
  
  /**
   * Sanitize volume values
   */
  private static sanitizeVolume(value: any): number {
    const volume = typeof value === 'number' ? value : parseFloat(value);
    
    if (isNaN(volume) || volume < 0) {
      throw new Error(`Invalid volume: ${value}`);
    }
    
    return volume;
  }
  
  /**
   * Fix OHLC relationships if possible
   */
  private static fixOHLCRelationships(bar: any): any {
    const prices = [bar.open, bar.high, bar.low, bar.close];
    
    // Ensure high is the maximum and low is the minimum
    const correctedHigh = Math.max(...prices);
    const correctedLow = Math.min(...prices);
    
    return {
      ...bar,
      high: correctedHigh,
      low: correctedLow
    };
  }
  
  /**
   * Format Zod error for better readability
   */
  private static formatZodError(error: z.ZodError): string {
    return error.issues
      .map(issue => {
        const path = issue.path.join('.');
        return `${path}: ${issue.message}`;
      })
      .join('; ');
  }
  
  /**
   * Record common errors for analysis
   */
  private static recordCommonError(error: string): void {
    const count = this.validationMetrics.commonErrors.get(error) || 0;
    this.validationMetrics.commonErrors.set(error, count + 1);
  }
  
  /**
   * Get validation metrics
   */
  static getMetrics() {
    const { totalValidations, successfulValidations, failedValidations } = this.validationMetrics;
    const successRate = totalValidations > 0 ? (successfulValidations / totalValidations) * 100 : 100;
    
    return {
      totalValidations,
      successfulValidations,
      failedValidations,
      successRate,
      commonErrors: Object.fromEntries(this.validationMetrics.commonErrors)
    };
  }
  
  /**
   * Reset validation metrics
   */
  static resetMetrics(): void {
    this.validationMetrics = {
      totalValidations: 0,
      successfulValidations: 0,
      failedValidations: 0,
      commonErrors: new Map()
    };
  }
}

/**
 * Type guards for runtime type checking
 */
export const TypeGuards = {
  isOHLCData: (obj: unknown): obj is OHLCData => {
    try {
      OHLCBarSchema.parse(obj);
      return true;
    } catch {
      return false;
    }
  },
  
  isChartData: (obj: unknown): obj is ChartData => {
    try {
      ChartDataSchema.parse(obj);
      return true;
    } catch {
      return false;
    }
  },
  
  isWebSocketMessage: (obj: unknown): obj is WebSocketMessage => {
    try {
      WebSocketMessageSchema.parse(obj);
      return true;
    } catch {
      return false;
    }
  }
};

/**
 * Validation decorators for class methods
 */
export function validateInput<T>(schema: z.ZodSchema<T>) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = function (...args: any[]) {
      try {
        const validatedArgs = args.map(arg => schema.parse(arg));
        return originalMethod.apply(this, validatedArgs);
      } catch (error) {
        if (error instanceof z.ZodError) {
          throw new ChartDataError(`Validation failed for ${propertyKey}: ${DataValidator['formatZodError'](error)}`);
        }
        throw error;
      }
    };
    
    return descriptor;
  };
}