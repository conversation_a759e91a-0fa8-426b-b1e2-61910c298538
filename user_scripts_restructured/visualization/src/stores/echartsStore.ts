/**
 * ECharts Pinia Store
 * 
 * Centralized state management for ECharts visualization,
 * inspired by FreqUI's state management patterns.
 */

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

// Types
interface ChartData {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface InstrumentInfo {
  id: string;
  name: string;
  symbol: string;
  exchange: string;
}

interface ChartConfig {
  theme: 'dark' | 'light';
  showVolume: boolean;
  showGrid: boolean;
  enableInfiniteScroll: boolean;
  samplingStrategy: 'none' | 'uniform' | 'adaptive' | 'time_based';
  maxDataPoints: number;
  loadChunkSize: number;
  memoryThreshold: number;
}

interface LoadingState {
  isLoading: boolean;
  loadingDirection: 'left' | 'right' | null;
  loadingProgress: number;
}

interface InfiniteScrollState {
  hasMoreHistorical: boolean;
  hasMoreRecent: boolean;
  totalBarsLoaded: number;
  oldestTimestamp: string | null;
  newestTimestamp: string | null;
  isAtLeftEdge: boolean;
  isAtRightEdge: boolean;
}

interface PerformanceMetrics {
  lastLoadTime: number;
  averageLoadTime: number;
  memoryUsage: number;
  fps: number;
  totalRequests: number;
  errorCount: number;
}

export const useEChartsStore = defineStore('echarts', () => {
  // State
  const instruments = ref<InstrumentInfo[]>([]);
  const currentInstrument = ref<string>('');
  const currentTimeframe = ref<string>('1min');
  const chartData = ref<ChartData[]>([]);
  const connectionStatus = ref<'connected' | 'connecting' | 'disconnected'>('disconnected');
  const error = ref<string | null>(null);
  
  // Chart configuration
  const chartConfig = ref<ChartConfig>({
    theme: 'dark',
    showVolume: true,
    showGrid: true,
    enableInfiniteScroll: true,
    samplingStrategy: 'adaptive',
    maxDataPoints: 10000,
    loadChunkSize: 100,
    memoryThreshold: 80
  });
  
  // Loading state
  const loadingState = ref<LoadingState>({
    isLoading: false,
    loadingDirection: null,
    loadingProgress: 0
  });
  
  // Infinite scroll state
  const infiniteScrollState = ref<InfiniteScrollState>({
    hasMoreHistorical: true,
    hasMoreRecent: true,
    totalBarsLoaded: 0,
    oldestTimestamp: null,
    newestTimestamp: null,
    isAtLeftEdge: false,
    isAtRightEdge: false
  });
  
  // Performance metrics
  const performanceMetrics = ref<PerformanceMetrics>({
    lastLoadTime: 0,
    averageLoadTime: 0,
    memoryUsage: 0,
    fps: 60,
    totalRequests: 0,
    errorCount: 0
  });
  
  // WebSocket connection
  const wsConnection = ref<WebSocket | null>(null);
  const wsReconnectAttempts = ref(0);
  const wsMaxReconnectAttempts = ref(5);
  
  // Computed
  const isConnected = computed(() => connectionStatus.value === 'connected');
  const isLoading = computed(() => loadingState.value.isLoading);
  const canLoadMore = computed(() => 
    infiniteScrollState.value.hasMoreHistorical || infiniteScrollState.value.hasMoreRecent
  );
  const memoryLevel = computed(() => {
    const usage = performanceMetrics.value.memoryUsage;
    if (usage < 50) return 'low';
    if (usage < 75) return 'moderate';
    return 'high';
  });
  const averageFPS = computed(() => Math.round(performanceMetrics.value.fps));
  
  // Actions
  const initialize = async () => {
    console.log('🏪 Store initialize() called...');
    
    try {
      // Load available instruments
      console.log('📡 Fetching instruments from /api/instruments...');
      const response = await fetch('/api/instruments');
      
      console.log('🌐 Response received:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        url: response.url
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log('📊 Raw instruments data:', data);
        
        instruments.value = data.instruments || data || []; // Try both formats
        console.log('📋 Instruments set in store:', instruments.value);
        
        if (instruments.value.length > 0) {
          if (!currentInstrument.value) {
            currentInstrument.value = instruments.value[0].id || instruments.value[0].symbol || instruments.value[0];
            console.log('🎯 Auto-selected instrument:', currentInstrument.value);
          }
        } else {
          console.warn('⚠️ No instruments found in response');
        }
      } else {
        console.error('💥 API request failed:', {
          status: response.status,
          statusText: response.statusText,
          url: response.url
        });
        setError(`Failed to load instruments: ${response.status} ${response.statusText}`);
      }
    } catch (err) {
      console.error('💥 Failed to initialize store:', err);
      console.error('Error details:', {
        message: err.message,
        stack: err.stack,
        name: err.name
      });
      setError('Failed to load instruments');
    }
  };
  
  const setCurrentInstrument = (instrument: string) => {
    currentInstrument.value = instrument;
    resetChartData();
  };
  
  const setCurrentTimeframe = (timeframe: string) => {
    currentTimeframe.value = timeframe;
    resetChartData();
  };
  
  const setConnectionStatus = (status: typeof connectionStatus.value) => {
    connectionStatus.value = status;
    
    if (status === 'connected') {
      wsReconnectAttempts.value = 0;
    }
  };
  
  const setError = (message: string | null) => {
    error.value = message;
    
    if (message) {
      performanceMetrics.value.errorCount++;
    }
  };
  
  const updateChartData = (newData: ChartData[]) => {
    chartData.value = newData;
    
    if (newData.length > 0) {
      infiniteScrollState.value.totalBarsLoaded = newData.length;
      infiniteScrollState.value.oldestTimestamp = newData[0].time;
      infiniteScrollState.value.newestTimestamp = newData[newData.length - 1].time;
    }
  };
  
  const appendChartData = (newData: ChartData[], direction: 'left' | 'right') => {
    if (direction === 'left') {
      chartData.value = [...newData, ...chartData.value];
      if (newData.length > 0) {
        infiniteScrollState.value.oldestTimestamp = newData[0].time;
      }
    } else {
      chartData.value = [...chartData.value, ...newData];
      if (newData.length > 0) {
        infiniteScrollState.value.newestTimestamp = newData[newData.length - 1].time;
      }
    }
    
    infiniteScrollState.value.totalBarsLoaded = chartData.value.length;
    
    // Memory management
    if (chartData.value.length > chartConfig.value.maxDataPoints) {
      trimChartData();
    }
  };
  
  const trimChartData = () => {
    const maxPoints = chartConfig.value.maxDataPoints;
    const currentLength = chartData.value.length;
    
    if (currentLength > maxPoints) {
      const trimAmount = Math.floor((currentLength - maxPoints) / 2);
      chartData.value = chartData.value.slice(trimAmount, currentLength - trimAmount);
      infiniteScrollState.value.totalBarsLoaded = chartData.value.length;
      
      // Update timestamps
      if (chartData.value.length > 0) {
        infiniteScrollState.value.oldestTimestamp = chartData.value[0].time;
        infiniteScrollState.value.newestTimestamp = chartData.value[chartData.value.length - 1].time;
      }
    }
  };
  
  const resetChartData = () => {
    chartData.value = [];
    infiniteScrollState.value = {
      hasMoreHistorical: true,
      hasMoreRecent: true,
      totalBarsLoaded: 0,
      oldestTimestamp: null,
      newestTimestamp: null,
      isAtLeftEdge: false,
      isAtRightEdge: false
    };
  };
  
  const setLoadingState = (loading: boolean, direction?: 'left' | 'right' | null) => {
    loadingState.value.isLoading = loading;
    loadingState.value.loadingDirection = direction || null;
    
    if (!loading) {
      loadingState.value.loadingProgress = 0;
    }
  };
  
  const updateLoadingProgress = (progress: number) => {
    loadingState.value.loadingProgress = Math.max(0, Math.min(100, progress));
  };
  
  const updatePerformanceMetrics = (metrics: Partial<PerformanceMetrics>) => {
    Object.assign(performanceMetrics.value, metrics);
  };
  
  const recordLoadTime = (loadTime: number) => {
    performanceMetrics.value.lastLoadTime = loadTime;
    performanceMetrics.value.totalRequests++;
    
    // Calculate rolling average
    const currentAvg = performanceMetrics.value.averageLoadTime;
    const totalRequests = performanceMetrics.value.totalRequests;
    performanceMetrics.value.averageLoadTime = 
      (currentAvg * (totalRequests - 1) + loadTime) / totalRequests;
  };
  
  const updateMemoryUsage = (usage: number) => {
    performanceMetrics.value.memoryUsage = usage;
  };
  
  const updateFPS = (fps: number) => {
    performanceMetrics.value.fps = fps;
  };
  
  const updateChartConfig = (config: Partial<ChartConfig>) => {
    Object.assign(chartConfig.value, config);
  };
  
  const setInfiniteScrollState = (state: Partial<InfiniteScrollState>) => {
    Object.assign(infiniteScrollState.value, state);
  };
  
  // WebSocket actions
  const setWebSocketConnection = (ws: WebSocket | null) => {
    wsConnection.value = ws;
  };
  
  const incrementReconnectAttempts = () => {
    wsReconnectAttempts.value++;
  };
  
  const resetReconnectAttempts = () => {
    wsReconnectAttempts.value = 0;
  };
  
  const canReconnect = computed(() => 
    wsReconnectAttempts.value < wsMaxReconnectAttempts.value
  );
  
  return {
    // State
    instruments,
    currentInstrument,
    currentTimeframe,
    chartData,
    connectionStatus,
    error,
    chartConfig,
    loadingState,
    infiniteScrollState,
    performanceMetrics,
    wsConnection,
    wsReconnectAttempts,
    
    // Computed
    isConnected,
    isLoading,
    canLoadMore,
    memoryLevel,
    averageFPS,
    canReconnect,
    
    // Actions
    initialize,
    setCurrentInstrument,
    setCurrentTimeframe,
    setConnectionStatus,
    setError,
    updateChartData,
    appendChartData,
    trimChartData,
    resetChartData,
    setLoadingState,
    updateLoadingProgress,
    updatePerformanceMetrics,
    recordLoadTime,
    updateMemoryUsage,
    updateFPS,
    updateChartConfig,
    setInfiniteScrollState,
    setWebSocketConnection,
    incrementReconnectAttempts,
    resetReconnectAttempts
  };
});