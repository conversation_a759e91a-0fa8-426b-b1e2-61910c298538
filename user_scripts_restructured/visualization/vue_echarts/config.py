"""
Vue-ECharts visualization module for Nautilus Trader.

This module provides configuration classes and utilities specifically
for the Vue.js + ECharts visualization component.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional
import logging
import os

# Note: Core dependencies are not available in this refactored version
# Using standalone configuration instead
ConfigManager = None
Config = None
VisualizationConfig = None

logger = logging.getLogger(__name__)


@dataclass
class ChartConfig:
    """Configuration for Vue-ECharts visualizer."""

    # Server configuration
    host: str = "0.0.0.0"
    port: int = 8080
    debug: bool = False
    log_level: str = "INFO"

    # Data configuration
    catalog_path: str = "/home/<USER>/nautilus_trader_fork/catalog"
    max_points: int = 50000  # Per-chunk limit for infinite scroll (much higher than old total dataset limit)
    cache_expiry: int = 1800  # 30 minutes
    max_cache_items: int = 500  # Increased for chunk-based caching

    # Chart-specific configuration
    enable_websocket: bool = False
    websocket_port: int = 8081
    enable_test_data: bool = False
    test_symbols: str = "TEST,AAPL,MSFT"
    test_bars: int = 1000

    # Performance configuration
    chunk_size: int = 10000  # Default chunk size for infinite scroll data loading
    use_pyarrow_optimization: bool = True
    memory_limit_gb: float = 4.0
    
    # Pre-warming configuration for ultra-smooth user experience
    enable_prewarming: bool = True  # Enable data pre-warming for <60ms responses
    max_prewarming_memory_mb: int = 300  # Maximum memory for pre-warmed data (conservative)
    max_concurrent_processing: int = 2  # Max concurrent instruments to process during startup
    startup_timeout_seconds: int = 180  # Maximum time allowed for pre-warming (3 minutes)
    
    # Smart cache segmentation for <10ms responses
    enable_smart_segmentation: bool = True  # Enable intelligent cache segmentation
    segment_size: int = 50000  # Bars per segment for optimal memory/performance balance
    max_segments_per_instrument: int = 50  # Maximum segments to prevent memory bloat

    # UI configuration
    default_timeframe: str = "1min"
    available_timeframes: list = field(default_factory=lambda: [
        "1min", "5min", "15min", "30min", "1h", "4h", "1d", "1w"
    ])

    @classmethod
    def from_main_config(cls, main_config=None, **overrides) -> 'ChartConfig':
        """
        Create ChartConfig from main application configuration.

        Args:
            main_config: Main application configuration (optional, not used in standalone mode)
            **overrides: Additional configuration overrides

        Returns:
            ChartConfig instance
        """
        # In standalone mode, just use defaults with overrides
        config_dict = {}

        # If main_config is provided and has the expected structure, use it
        if main_config and hasattr(main_config, 'catalog'):
            try:
                config_dict.update({
                    'catalog_path': main_config.catalog.base_path,
                    'log_level': main_config.logging.level,
                    'debug': main_config.logging.level.upper() == 'DEBUG',
                    'memory_limit_gb': getattr(main_config.processing, 'memory_limit_gb', 4.0),
                    'chunk_size': getattr(main_config.processing, 'chunk_size', 10000),
                })

                # Add visualization-specific settings
                if hasattr(main_config, 'visualization') and main_config.visualization:
                    chart_config = main_config.visualization.vue_echarts
                    config_dict.update({
                        'host': chart_config.get('host', '0.0.0.0'),
                        'port': chart_config.get('port', 8080),
                        'websocket_port': chart_config.get('websocket_port', 8081),
                        'max_points': chart_config.get('max_points', 5000),
                        'cache_expiry': chart_config.get('cache_expiry', 1800),
                        'enable_websocket': chart_config.get('enable_websocket', False),
                        'enable_test_data': chart_config.get('enable_test_data', False),
                        'test_symbols': chart_config.get('test_symbols', 'MNQCONT.CME,MNQ.CME,ES,NQ'),
                        'test_bars': chart_config.get('test_bars', 1000),
                    })
            except AttributeError:
                # If main_config doesn't have expected structure, just use defaults
                pass

        # Apply any overrides
        config_dict.update(overrides)

        return cls(**config_dict)

    @classmethod
    def from_env_and_args(cls, args: Optional[Any] = None) -> 'ChartConfig':
        """
        Create ChartConfig from environment variables and command line arguments.

        Args:
            args: Parsed command line arguments (optional)

        Returns:
            ChartConfig instance
        """
        # Start with defaults
        config_dict = {}

        # Load from environment variables
        env_mappings = {
            'CHART_HOST': 'host',
            'CHART_PORT': 'port',
            'CHART_DEBUG': 'debug',
            'CHART_LOG_LEVEL': 'log_level',
            'CATALOG_PATH': 'catalog_path',
            'CHART_MAX_POINTS': 'max_points',
            'CHART_CACHE_EXPIRY': 'cache_expiry',
            'CHART_ENABLE_WEBSOCKET': 'enable_websocket',
            'CHART_WEBSOCKET_PORT': 'websocket_port',
            'CHART_ENABLE_TEST_DATA': 'enable_test_data',
            'CHART_TEST_SYMBOLS': 'test_symbols',
            'CHART_TEST_BARS': 'test_bars',
            'CHART_ENABLE_PREWARMING': 'enable_prewarming',
        }

        for env_var, config_key in env_mappings.items():
            if env_var in os.environ:
                value = os.environ[env_var]

                # Convert types as needed
                if config_key in ['port', 'max_points', 'cache_expiry', 'websocket_port', 'test_bars']:
                    value = int(value)
                elif config_key in ['debug', 'enable_websocket', 'enable_test_data', 'enable_prewarming']:
                    value = value.lower() in ('true', '1', 'yes', 'on')
                elif config_key in ['memory_limit_gb']:
                    value = float(value)

                config_dict[config_key] = value

        # Override with command line arguments if provided
        if args:
            arg_mappings = {
                'host': 'host',
                'port': 'port',
                'debug': 'debug',
                'log_level': 'log_level',
                'catalog': 'catalog_path',
                'max_points': 'max_points',
                'enable_websocket': 'enable_websocket',
                'websocket_port': 'websocket_port',
                'test_data': 'enable_test_data',
                'symbols': 'test_symbols',
                'bars': 'test_bars',
            }

            for arg_name, config_key in arg_mappings.items():
                if hasattr(args, arg_name) and getattr(args, arg_name) is not None:
                    config_dict[config_key] = getattr(args, arg_name)

        return cls(**config_dict)

    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary format."""
        from dataclasses import asdict
        return asdict(self)

    def validate(self) -> bool:
        """
        Validate the configuration settings.

        Returns:
            True if configuration is valid, False otherwise
        """
        try:
            # Validate port ranges
            if not (1024 <= self.port <= 65535):
                logger.error(f"Invalid port number: {self.port}")
                return False

            if self.enable_websocket and not (1024 <= self.websocket_port <= 65535):
                logger.error(f"Invalid WebSocket port number: {self.websocket_port}")
                return False

            # Validate memory limits
            if self.memory_limit_gb <= 0:
                logger.error(f"Invalid memory limit: {self.memory_limit_gb}")
                return False

            # Validate cache settings
            if self.cache_expiry <= 0:
                logger.error(f"Invalid cache expiry: {self.cache_expiry}")
                return False

            if self.max_cache_items <= 0:
                logger.error(f"Invalid max cache items: {self.max_cache_items}")
                return False

            # Validate timeframes
            if self.default_timeframe not in self.available_timeframes:
                logger.error(f"Default timeframe '{self.default_timeframe}' not in available timeframes")
                return False

            return True

        except Exception as e:
            logger.error(f"Configuration validation error: {e}")
            return False

    def get_server_url(self) -> str:
        """Get the server URL."""
        return f"http://{self.host}:{self.port}"

    def get_websocket_url(self) -> str:
        """Get the WebSocket server URL."""
        # Flask-SocketIO runs on the same port as the HTTP server
        return f"http://{self.host}:{self.port}"


class ChartConfigManager:
    """Manages chart-specific configuration loading and validation."""

    @staticmethod
    def load_config(config_path: Optional[str] = None, **overrides) -> ChartConfig:
        """
        Load chart configuration from multiple sources.

        Args:
            config_path: Path to main configuration file (not used in standalone mode)
            **overrides: Additional configuration overrides

        Returns:
            ChartConfig instance
        """
        try:
            # In standalone mode, create configuration from environment and overrides
            chart_config = ChartConfig.from_env_and_args(None)

            # Apply any additional overrides
            if overrides:
                config_dict = chart_config.to_dict()
                config_dict.update(overrides)
                chart_config = ChartConfig(**config_dict)

            # Validate configuration
            if not chart_config.validate():
                logger.warning("Chart configuration validation failed, using defaults")
                chart_config = ChartConfig()

            logger.info(f"Loaded chart configuration: {chart_config.get_server_url()}")
            return chart_config

        except Exception as e:
            logger.error(f"Error loading chart configuration: {e}")
            logger.info("Using default chart configuration")
            return ChartConfig()

    @staticmethod
    def create_from_args(args) -> ChartConfig:
        """
        Create chart configuration from command line arguments.

        Args:
            args: Parsed command line arguments

        Returns:
            ChartConfig instance
        """
        return ChartConfig.from_env_and_args(args)
