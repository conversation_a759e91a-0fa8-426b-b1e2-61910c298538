<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue-ECharts Diagnostic Tool</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #ffffff;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            color: #00d4aa;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            background: #2d2d2d;
            border: 1px solid #404040;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .test-section h2 {
            color: #ffffff;
            margin-top: 0;
            margin-bottom: 15px;
            border-bottom: 1px solid #404040;
            padding-bottom: 10px;
        }
        
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
        }
        
        .success {
            background: #1b4d3e;
            border: 1px solid #00d4aa;
            color: #00d4aa;
        }
        
        .error {
            background: #4d1b1b;
            border: 1px solid #ff4444;
            color: #ff4444;
        }
        
        .warning {
            background: #4d4d1b;
            border: 1px solid #ffaa00;
            color: #ffaa00;
        }
        
        .info {
            background: #1b3d4d;
            border: 1px solid #4488ff;
            color: #4488ff;
        }
        
        button {
            background: #00d4aa;
            color: #000000;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        
        button:hover {
            background: #00b890;
        }
        
        button:disabled {
            background: #666666;
            cursor: not-allowed;
        }
        
        pre {
            background: #000000;
            color: #ffffff;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .progress {
            width: 100%;
            height: 20px;
            background: #404040;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-bar {
            height: 100%;
            background: #00d4aa;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Vue-ECharts Diagnostic Tool</h1>
        
        <div class="test-section">
            <h2>🌐 Server Connectivity Tests</h2>
            <button onclick="testServerConnectivity()">Run Server Tests</button>
            <div id="server-results"></div>
        </div>
        
        <div class="test-section">
            <h2>📦 Static File Tests</h2>
            <button onclick="testStaticFiles()">Test Static Files</button>
            <div id="static-results"></div>
        </div>
        
        <div class="test-section">
            <h2>🎯 API Endpoint Tests</h2>
            <button onclick="testAPIEndpoints()">Test APIs</button>
            <div id="api-results"></div>
        </div>
        
        <div class="test-section">
            <h2>🚀 Vue-ECharts Application Tests</h2>
            <button onclick="testVueECharts()">Test Vue-ECharts Bundle</button>
            <div id="vue-results"></div>
        </div>
        
        <div class="test-section">
            <h2>🔧 Browser Environment</h2>
            <button onclick="testBrowserEnvironment()">Check Browser</button>
            <div id="browser-results"></div>
        </div>
        
        <div class="test-section">
            <h2>📊 Live Chart Test</h2>
            <button onclick="openChartTest()">Open Chart in New Tab</button>
            <button onclick="embedChart()">Embed Chart Here</button>
            <div id="chart-container" style="height: 400px; border: 1px solid #404040; margin-top: 10px; display: none;"></div>
        </div>
        
        <div class="test-section">
            <h2>📝 Console Logs</h2>
            <button onclick="clearLogs()">Clear Logs</button>
            <pre id="console-logs"></pre>
        </div>
    </div>

    <script>
        // Console log capture
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        let logs = [];
        
        function addLog(type, message) {
            const timestamp = new Date().toISOString().substr(11, 12);
            logs.push(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
            updateLogDisplay();
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addLog('log', args.join(' '));
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addLog('error', args.join(' '));
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addLog('warn', args.join(' '));
        };
        
        function updateLogDisplay() {
            const logElement = document.getElementById('console-logs');
            logElement.textContent = logs.join('\\n');
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearLogs() {
            logs = [];
            updateLogDisplay();
        }
        
        function addResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            container.appendChild(div);
        }
        
        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }
        
        async function testServerConnectivity() {
            clearResults('server-results');
            addResult('server-results', 'info', '🔍 Testing server connectivity...');
            
            try {
                const response = await fetch('/api/health');
                if (response.ok) {
                    addResult('server-results', 'success', '✅ Server is responding');
                    const data = await response.json();
                    addResult('server-results', 'success', `✅ Health check: ${JSON.stringify(data, null, 2)}`);
                } else {
                    addResult('server-results', 'error', `❌ Server error: ${response.status}`);
                }
            } catch (error) {
                addResult('server-results', 'error', `❌ Connection failed: ${error.message}`);
            }
        }
        
        async function testStaticFiles() {
            clearResults('static-results');
            addResult('static-results', 'info', '📦 Testing static file access...');
            
            const staticFiles = [
                '/static/js/vue-echarts-app.js',
                '/static/js/assets/vue-echarts-app-D5TJhZiu.css'
            ];
            
            for (const file of staticFiles) {
                try {
                    const response = await fetch(file);
                    if (response.ok) {
                        const size = response.headers.get('content-length') || 'unknown';
                        addResult('static-results', 'success', `✅ ${file} (${size} bytes)`);
                    } else {
                        addResult('static-results', 'error', `❌ ${file}: ${response.status}`);
                    }
                } catch (error) {
                    addResult('static-results', 'error', `❌ ${file}: ${error.message}`);
                }
            }
        }
        
        async function testAPIEndpoints() {
            clearResults('api-results');
            addResult('api-results', 'info', '🎯 Testing API endpoints...');
            
            // Test instruments API
            try {
                const response = await fetch('/api/instruments');
                if (response.ok) {
                    const data = await response.json();
                    addResult('api-results', 'success', `✅ Instruments API: ${data.length} instruments`);
                    if (data.length > 0) {
                        addResult('api-results', 'info', `📊 Sample: ${data[0].id}`);
                    }
                } else {
                    addResult('api-results', 'error', `❌ Instruments API: ${response.status}`);
                }
            } catch (error) {
                addResult('api-results', 'error', `❌ Instruments API: ${error.message}`);
            }
            
            // Test chart data API
            try {
                const response = await fetch('/api/chart-data/MNQ.CME?limit=1');
                if (response.ok) {
                    const data = await response.json();
                    const ohlcCount = data.ohlc ? data.ohlc.length : 0;
                    addResult('api-results', 'success', `✅ Chart Data API: ${ohlcCount} bars`);
                    if (data.ohlc && data.ohlc[0]) {
                        const sample = data.ohlc[0];
                        addResult('api-results', 'info', `💰 Sample: O=${sample.open} H=${sample.high} L=${sample.low} C=${sample.close}`);
                    }
                } else {
                    addResult('api-results', 'error', `❌ Chart Data API: ${response.status}`);
                }
            } catch (error) {
                addResult('api-results', 'error', `❌ Chart Data API: ${error.message}`);
            }
        }
        
        async function testVueECharts() {
            clearResults('vue-results');
            addResult('vue-results', 'info', '🚀 Testing Vue-ECharts bundle...');
            
            try {
                // Test if the module can be imported
                const module = await import('/static/js/vue-echarts-app.js');
                addResult('vue-results', 'success', '✅ Vue-ECharts bundle loaded successfully');
                addResult('vue-results', 'info', `📦 Module exports: ${Object.keys(module).join(', ')}`);
            } catch (error) {
                addResult('vue-results', 'error', `❌ Vue-ECharts bundle failed: ${error.message}`);
                addResult('vue-results', 'error', `🔧 Error stack: ${error.stack}`);
            }
        }
        
        function testBrowserEnvironment() {
            clearResults('browser-results');
            addResult('browser-results', 'info', '🔧 Checking browser environment...');
            
            // Check JavaScript support
            addResult('browser-results', 'success', `✅ JavaScript: Enabled`);
            addResult('browser-results', 'success', `✅ User Agent: ${navigator.userAgent}`);
            
            // Check ES6 module support
            if ('import' in window.__proto__.constructor) {
                addResult('browser-results', 'success', '✅ ES6 Modules: Supported');
            } else {
                addResult('browser-results', 'warning', '⚠️ ES6 Modules: Limited support');
            }
            
            // Check WebSocket support
            if (typeof WebSocket !== 'undefined') {
                addResult('browser-results', 'success', '✅ WebSocket: Supported');
            } else {
                addResult('browser-results', 'error', '❌ WebSocket: Not supported');
            }
            
            // Check memory info
            if (performance.memory) {
                const memory = performance.memory;
                addResult('browser-results', 'info', `📊 Memory: ${Math.round(memory.usedJSHeapSize / 1024 / 1024)}MB used`);
            }
            
            // Check network info
            if (navigator.connection) {
                addResult('browser-results', 'info', `🌐 Connection: ${navigator.connection.effectiveType}`);
            }
        }
        
        function openChartTest() {
            window.open('/chart/MNQ.CME', '_blank');
        }
        
        function embedChart() {
            const container = document.getElementById('chart-container');
            container.style.display = 'block';
            container.innerHTML = '<iframe src="/chart/MNQ.CME" style="width: 100%; height: 100%; border: none;"></iframe>';
        }
        
        // Initialize
        window.addEventListener('load', () => {
            console.log('Vue-ECharts Diagnostic Tool loaded');
            addResult('console-logs', 'info', '🔍 Diagnostic tool ready');
        });
        
        // Capture global errors
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
        });
    </script>
</body>
</html>