"""
Vue-ECharts visualization module for Nautilus Trader.

This module provides WebSocket-enabled Flask application for real-time
chart updates using Vue.js + ECharts components with Flask-SocketIO.
"""

import os
import time
import logging
import threading
import random
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, Dict, Any

try:
    import pyarrow as pa
    PYARROW_AVAILABLE = True
except ImportError:
    PYARROW_AVAILABLE = False
    pa = None

try:
    from flask import Flask, render_template, jsonify, request, redirect
    from flask_socketio import SocketIO, emit, disconnect
    FLASK_SOCKETIO_AVAILABLE = True
except ImportError:
    FLASK_SOCKETIO_AVAILABLE = False
    Flask = None
    SocketIO = None
    emit = None
    disconnect = None

try:
    from ...core.config import ConfigManager
    from ..base_visualizer import BaseVisualizer
except ImportError:
    ConfigManager = None
    BaseVisualizer = None

from .config import ChartConfig, ChartConfigManager
from .data_loader import load_instrument_data, get_available_instruments, set_catalog_path, ChartDataLoader, validate_catalog_configuration
from .echarts_data_loader import EChartsDataLoader, EChartsLoadRequest, SamplingStrategy
from .utils.chart_utils import create_chart_response
from .utils.test_data import create_test_catalog
from .prewarming_manager import PreWarmingManager, get_prewarming_manager, start_prewarming_async

logger = logging.getLogger(__name__)


class ConnectionManager:
    """
    Enterprise-grade connection manager for Flask-SocketIO following production best practices.
    
    This class implements proper connection lifecycle management with health monitoring,
    automatic cleanup, and thread-safe operations as recommended in the bug analysis.
    """
    
    def __init__(self, socketio):
        self.socketio = socketio
        self._connections = {}
        self._lock = threading.RLock()  # Reentrant lock for nested operations
        self._cleanup_thread = None
        self._stop_cleanup = False
        self._health_check_interval = 30  # seconds
        self._connection_timeout = 300  # 5 minutes
        
    def add_connection(self, sid: str, data: Dict[str, Any]) -> None:
        """Add a new connection with tracking data."""
        with self._lock:
            self._connections[sid] = {
                'data': data,
                'last_seen': time.time(),
                'health_check_count': 0,
                'connection_time': time.time(),
                'message_count': 0
            }
            logger.debug(f"Added connection {sid}, total connections: {len(self._connections)}")
    
    def update_connection_activity(self, sid: str) -> None:
        """Update the last activity time for a connection."""
        with self._lock:
            if sid in self._connections:
                self._connections[sid]['last_seen'] = time.time()
                self._connections[sid]['message_count'] += 1
    
    def remove_connection(self, sid: str) -> Optional[Dict[str, Any]]:
        """Remove a connection and return its data."""
        with self._lock:
            connection_data = self._connections.pop(sid, None)
            if connection_data:
                logger.debug(f"Removed connection {sid}, total connections: {len(self._connections)}")
            return connection_data
    
    def get_connection(self, sid: str) -> Optional[Dict[str, Any]]:
        """Get connection data for a specific session ID."""
        with self._lock:
            return self._connections.get(sid)
    
    def get_all_connections(self) -> Dict[str, Dict[str, Any]]:
        """Get a thread-safe copy of all connections."""
        with self._lock:
            return dict(self._connections)
    
    def get_connection_count(self) -> int:
        """Get the current number of active connections."""
        with self._lock:
            return len(self._connections)
    
    def cleanup_stale_connections(self) -> int:
        """
        Cleanup stale connections following Flask-SocketIO health check pattern.
        Returns the number of connections cleaned up.
        """
        cleaned_count = 0
        current_time = time.time()
        
        with self._lock:
            stale_sids = [
                sid for sid, info in self._connections.items()
                if current_time - info['last_seen'] > self._connection_timeout
            ]
            
            for sid in stale_sids:
                try:
                    # Check if connection is actually still active
                    if self.socketio.server.manager.is_connected(sid, namespace='/'):
                        # Connection is active, update last_seen
                        self._connections[sid]['last_seen'] = current_time
                        self._connections[sid]['health_check_count'] += 1
                    else:
                        # Connection is stale, remove it
                        self._connections.pop(sid, None)
                        cleaned_count += 1
                        logger.debug(f"Cleaned up stale connection: {sid}")
                except Exception as e:
                    # Connection check failed, assume stale
                    self._connections.pop(sid, None)
                    cleaned_count += 1
                    logger.debug(f"Cleaned up problematic connection {sid}: {e}")
        
        if cleaned_count > 0:
            logger.info(f"Cleaned up {cleaned_count} stale connections")
        
        return cleaned_count
    
    def start_health_monitoring(self) -> None:
        """Start background health monitoring thread."""
        if self._cleanup_thread is None or not self._cleanup_thread.is_alive():
            self._stop_cleanup = False
            self._cleanup_thread = threading.Thread(
                target=self._health_monitor_loop, 
                daemon=True,
                name="SocketIO-HealthMonitor"
            )
            self._cleanup_thread.start()
            logger.info("Connection health monitoring started")
    
    def stop_health_monitoring(self) -> None:
        """Stop background health monitoring thread."""
        self._stop_cleanup = True
        if self._cleanup_thread and self._cleanup_thread.is_alive():
            self._cleanup_thread.join(timeout=5)
            logger.info("Connection health monitoring stopped")
    
    def _health_monitor_loop(self) -> None:
        """Background health monitoring loop."""
        while not self._stop_cleanup:
            try:
                self.cleanup_stale_connections()
                self._emit_health_metrics()
                time.sleep(self._health_check_interval)
            except Exception as e:
                logger.error(f"Error in health monitor loop: {e}")
                time.sleep(5)  # Shorter retry on error
    
    def _emit_health_metrics(self) -> None:
        """Emit health metrics for monitoring."""
        with self._lock:
            total_connections = len(self._connections)
            if total_connections == 0:
                return
                
            # Calculate metrics
            current_time = time.time()
            active_connections = sum(
                1 for info in self._connections.values()
                if current_time - info['last_seen'] < 60  # Active in last minute
            )
            
            avg_connection_duration = sum(
                current_time - info['connection_time']
                for info in self._connections.values()
            ) / total_connections
            
            total_messages = sum(
                info['message_count'] for info in self._connections.values()
            )
        
        # Log health metrics
        logger.debug(f"Connection Health: {active_connections}/{total_connections} active, "
                    f"avg duration: {avg_connection_duration:.1f}s, "
                    f"total messages: {total_messages}")


logger = logging.getLogger(__name__)


class WebSocketChartServer:
    """
    WebSocket-enabled Vue-ECharts server.

    This class extends the basic chart server with WebSocket support for
    real-time data streaming and Vue-ECharts updates.
    """

    def __init__(self, config: ChartConfig):
        """
        Initialize the WebSocket chart server.

        Args:
            config: Chart configuration
        """
        if not FLASK_SOCKETIO_AVAILABLE:
            raise ImportError("Flask-SocketIO is required for WebSocketChartServer. Install with: pip install Flask-SocketIO")

        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.app = None
        self.socketio = None
        self._active_subscriptions = {}  # Track active chart subscriptions
        self._subscriptions_lock = threading.RLock()  # Reentrant lock for nested operations
        
        # Network request deduplication to prevent concurrent identical requests
        self._pending_data_requests = {}  # Track pending data requests to prevent duplicates
        self._request_lock = threading.RLock()  # Thread safety for request tracking
        self._update_thread = None
        self._stop_updates = False
        self._connection_manager = None  # Enterprise-grade connection manager
        
        # Initialize pre-warming system
        self._prewarming_thread = None
        self._prewarming_config = {
            'enable_prewarming': config.__dict__.get('enable_prewarming', True),
            'max_prewarming_memory_mb': config.__dict__.get('max_prewarming_memory_mb', 300),
            'max_concurrent_processing': config.__dict__.get('max_concurrent_processing', 2),
            'startup_timeout_seconds': config.__dict__.get('startup_timeout_seconds', 180)
        }
        
        # Set the catalog path for the data loader
        set_catalog_path(config.catalog_path)
        
        # Initialize enhanced data loader for performance and caching
        try:
            self.data_loader = ChartDataLoader(config.__dict__)
            logger.info("Enhanced data loader initialized for WebSocket server")
        except Exception as e:
            logger.warning(f"Failed to initialize enhanced data loader: {e}, using legacy loading")
            self.data_loader = None
            
        # Initialize ECharts-optimized data loader
        try:
            self.echarts_loader = EChartsDataLoader(config)
            logger.info("ECharts data loader initialized for optimized chart performance")
        except Exception as e:
            logger.warning(f"Failed to initialize ECharts data loader: {e}")
            self.echarts_loader = None
        
        # Validate catalog configuration and data availability
        validation_result = validate_catalog_configuration()
        if not validation_result['valid']:
            logger.error("❌ Catalog validation failed!")
            for error in validation_result['errors']:
                logger.error(f"   - {error}")
            for recommendation in validation_result['recommendations']:
                logger.info(f"💡 {recommendation}")
        else:
            logger.info(f"✅ Catalog validation passed: {validation_result['instruments_found']} instruments found")
        
        self._setup_flask_app()

    def start_server(self):
        """Start the WebSocket server with proper eventlet configuration."""
        try:
            logger.info(f"🚀 Starting Vue-ECharts WebSocket server on {self.config.host}:{self.config.port}")
            logger.info(f"📊 Chart URL: http://{self.config.host}:{self.config.port}/chart/<instrument>")
            logger.info(f"🔧 Using eventlet for WebSocket support")
            
            # Use socketio.run() which automatically configures eventlet
            self.socketio.run(
                self.app,
                host=self.config.host,
                port=self.config.port,
                debug=self.config.debug,
                log_output=self.config.debug
            )
        except KeyboardInterrupt:
            logger.info("🛑 Server stopped by user")
        except Exception as e:
            logger.error(f"❌ Server error: {e}")
            raise

    def _get_static_folder_path(self) -> Path:
        """
        Get static folder path with fallback options for deployment flexibility.
        
        Returns:
            Path to static folder
        """
        
        # Option 1: Check for config-specified static path
        if hasattr(self.config, 'static_folder') and self.config.static_folder:
            static_path = Path(self.config.static_folder)
            if static_path.exists():
                return static_path
        
        # Option 2: Standard location relative to current file
        # Path: user_scripts_restructured/visualization/static
        standard_path = Path(__file__).parent.parent / 'static'
        if standard_path.exists():
            return standard_path
        
        # Option 3: Alternative deployment location
        # Check in parent directories for static folder
        current_path = Path(__file__).parent
        for _ in range(3):  # Check up to 3 levels up
            alt_path = current_path / 'static'
            if alt_path.exists():
                return alt_path
            current_path = current_path.parent
        
        # Option 4: Create static folder in current directory if none found
        fallback_path = Path(__file__).parent / 'static'
        fallback_path.mkdir(exist_ok=True)
        return fallback_path

    def _setup_flask_app(self):
        """Set up the Flask application with WebSocket support."""
        import os
        from pathlib import Path
        
        # Get static folder path with fallback options for deployment flexibility
        static_path = self._get_static_folder_path()
        
        # Fix 1: Ensure template folder uses absolute path to resolve template loading issues
        template_path = Path(__file__).parent / 'templates'
        
        # Verify template path exists and log for debugging
        if not template_path.exists():
            logger.error(f"Template directory does not exist: {template_path}")
            raise FileNotFoundError(f"Template directory not found: {template_path}")
        
        logger.info(f"Using template folder: {template_path.absolute()}")
        logger.info(f"Available templates: {list(template_path.glob('*.html'))}")
        
        self.app = Flask(
            __name__,
            template_folder=str(template_path.absolute()),
            static_folder=str(static_path.absolute()) if static_path.exists() else None
        )

        # Configure Flask
        self.app.config['SECRET_KEY'] = 'nautilus-websocket-chart-server'
        
        # Add security headers including CSP to allow necessary JavaScript execution
        @self.app.after_request
        def add_security_headers(response):
            # Content Security Policy that allows necessary JavaScript operations
            csp_policy = (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-eval' 'unsafe-inline' "
                "https://unpkg.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; "
                "style-src 'self' 'unsafe-inline' "
                "https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; "
                "connect-src 'self' ws: wss: http: https:; "
                "img-src 'self' data: https:; "
                "font-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; "
                "object-src 'none'; "
                "base-uri 'self'"
            )
            response.headers['Content-Security-Policy'] = csp_policy
            
            # Additional security headers
            response.headers['X-Content-Type-Options'] = 'nosniff'
            response.headers['X-Frame-Options'] = 'DENY'
            response.headers['X-XSS-Protection'] = '1; mode=block'
            response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
            
            return response

        # Initialize SocketIO
        self.socketio = SocketIO(
            self.app,
            cors_allowed_origins="*",
            logger=self.config.debug,
            engineio_logger=self.config.debug,
            ping_timeout=60,
            ping_interval=25
        )

        # Initialize enterprise-grade connection manager
        self._connection_manager = ConnectionManager(self.socketio)
        self._connection_manager.start_health_monitoring()
        logger.info("Enterprise connection manager initialized with health monitoring")

        # Register routes and socket events
        self._register_routes()
        self._register_socket_events()

        # Set up logging
        if not self.config.debug:
            log = logging.getLogger('werkzeug')
            log.setLevel(logging.WARNING)

    def _register_routes(self):
        """Register Flask routes."""

        @self.app.route('/')
        def index():
            """Redirect to WebSocket index."""
            return redirect('/websocket')

        @self.app.route('/websocket')
        def websocket_index():
            """Render the WebSocket index page."""
            try:
                instruments = get_available_instruments()
                return render_template('websocket_index.html', instruments=instruments)
            except Exception as e:
                logger.error(f"Error loading WebSocket index: {e}")
                return render_template('websocket_index.html', instruments=[])

        @self.app.route('/chart/<instrument_id>')
        def unified_chart(instrument_id: str):
            """Render the Vue.js + ECharts chart interface (simplified - no feature flags)."""
            debug = request.args.get('debug', 'false').lower() == 'true'
            timeframe = request.args.get('timeframe', '1min')
            return render_template('vue_echarts_chart.html', 
                                   instrument_id=instrument_id, 
                                   timeframe=timeframe,
                                   debug=debug)

        @self.app.route('/echarts/chart/<instrument_id>')
        def echarts_chart(instrument_id: str):
            """Render the Vue.js + ECharts chart interface (redirected from legacy route)."""
            debug = request.args.get('debug', 'false').lower() == 'true'
            timeframe = request.args.get('timeframe', '1min')
            return render_template('vue_echarts_chart.html', 
                                   instrument_id=instrument_id, 
                                   timeframe=timeframe,
                                   debug=debug)

        @self.app.route('/vue/chart/<instrument_id>')
        def vue_echarts_chart(instrument_id: str):
            """Render the Vue.js + ECharts chart interface with reactive data management."""
            debug = request.args.get('debug', 'false').lower() == 'true'
            timeframe = request.args.get('timeframe', '1min')
            return render_template('vue_echarts_chart.html', 
                                   instrument_id=instrument_id, 
                                   timeframe=timeframe,
                                   debug=debug)

        @self.app.route('/diagnostic')
        def diagnostic():
            """Render the Vue-ECharts diagnostic tool."""
            return render_template('diagnostic.html')

        # Keep the regular HTTP API for fallback
        @self.app.route('/api/instruments')
        def get_instruments():
            """Return available instruments as JSON."""
            try:
                instruments = get_available_instruments()
                if not instruments:
                    return jsonify({
                        'error': 'No instruments found in catalog',
                        'debug_info': {
                            'catalog_path': self.config.catalog_path,
                            'bar_data_path': f"{self.config.catalog_path}/data/bar/",
                            'path_exists': os.path.exists(self.config.catalog_path),
                            'bar_path_exists': os.path.exists(f"{self.config.catalog_path}/data/bar/")
                        },
                        'suggestions': [
                            f"Check that catalog path exists: {self.config.catalog_path}",
                            f"Verify bar data directory exists: {self.config.catalog_path}/data/bar/",
                            "Ensure parquet files exist in instrument subdirectories"
                        ]
                    }), 404
                return jsonify(instruments)
            except Exception as e:
                logger.error(f"Error getting instruments: {e}")
                return jsonify({
                    'error': f'Failed to load instruments: {str(e)}',
                    'debug_info': {
                        'catalog_path': self.config.catalog_path,
                        'exception_type': type(e).__name__
                    }
                }), 500

        @self.app.route('/api/chart-data/<instrument_id>')
        def get_chart_data(instrument_id: str):
            """Return chart data for a specific instrument (for infinite scrolling)."""
            try:
                # Import validation at function level to avoid circular imports
                from .validation import validate_chart_data_request
                
                # Prepare request args for validation
                request_args = {
                    'instrument_id': instrument_id,
                    'timeframe': request.args.get('timeframe', '1min'),
                    'limit': request.args.get('limit', str(self.config.max_points)),
                    'before_timestamp_seconds': request.args.get('before_timestamp_seconds'),
                    'after_timestamp_seconds': request.args.get('after_timestamp_seconds')
                }
                
                # Validate request parameters
                is_valid, error_response, validated_params = validate_chart_data_request(request_args)
                if not is_valid:
                    logger.warning(f"Validation error for chart data request: {error_response}")
                    return jsonify(error_response), 400
                
                # Extract validated parameters
                instrument_id = validated_params['instrument_id']
                timeframe = validated_params['timeframe']
                limit = validated_params['limit']
                before_datetime = validated_params.get('before_datetime')
                after_datetime = validated_params.get('after_datetime')

                # Try pre-warmed data first for ultra-fast response
                prewarming_manager = get_prewarming_manager()
                result = None
                
                if prewarming_manager and prewarming_manager.is_prewarmed(instrument_id):
                    logger.info(f"⚡ Instrument {instrument_id} is pre-warmed - trying smart cache")
                    result = prewarming_manager.get_prewarmed_data(
                        instrument=instrument_id,
                        before_datetime=before_datetime,
                        limit=limit
                    )
                
                # Use enhanced data loader if smart cache didn't serve the data
                if not result and self.data_loader:
                    if prewarming_manager and prewarming_manager.is_prewarmed(instrument_id):
                        logger.info(f"🔄 Smart cache miss - using enhanced loader for {instrument_id}")
                    else:
                        logger.info(f"🔄 Loading {instrument_id} (standard cache)")
                    result = self.data_loader.load_data(
                        instrument=instrument_id,
                        timeframe=timeframe,
                        before_datetime=before_datetime,
                        limit=limit
                    )
                
                # Final fallback to legacy loading when no other option available
                if not result:
                    logger.warning("Enhanced data loader unavailable, using legacy loading")
                    chart_data = load_instrument_data(
                        instrument_id=instrument_id,
                        timeframe=timeframe,
                        limit=limit,
                        before_datetime=before_datetime
                    )
                    
                    # Check if we have valid chart data (dict format)
                    if not isinstance(chart_data, dict) or not chart_data.get('ohlc'):
                        # Get available instruments for debugging
                        available_instruments = get_available_instruments()
                        available_ids = [inst['id'] for inst in available_instruments] if available_instruments else []
                        
                        return jsonify({
                            'error': f'No data found for instrument: {instrument_id}',
                            'instrument': instrument_id,
                            'timeframe': timeframe,
                            'debug_info': {
                                'catalog_path': self.config.catalog_path,
                                'available_instruments': available_ids[:10],  # First 10 for debugging
                                'total_instruments_found': len(available_ids),
                                'data_type_received': type(chart_data).__name__,
                                'chart_data_keys': list(chart_data.keys()) if isinstance(chart_data, dict) else 'N/A'
                            },
                            'suggestions': [
                                f"Check instrument name: '{instrument_id}' (case-sensitive)",
                                f"Available instruments: {', '.join(available_ids[:5])}" if available_ids else "No instruments found in catalog",
                                f"Verify data exists in: {self.config.catalog_path}/data/bar/{instrument_id}/"
                            ]
                        }), 404
                    
                    # Chart data is already in expected format (dict)
                    result = {
                        'ohlc': chart_data.get('ohlc', []),
                        'volume': chart_data.get('volume', []),
                        'data_points': len(chart_data.get('ohlc', [])),
                        'cache_hit': chart_data.get('cache_hit', False),
                        'data_source': chart_data.get('data_source', 'legacy_fallback')
                    }
                
                # Convert to expected format
                response = {
                    'ohlc': result.get('ohlc', []),
                    'volume': result.get('volume', []),
                    'instrument': instrument_id,
                    'timeframe': timeframe,
                    'bars_returned': result.get('data_points', 0),
                    'statistics': {
                        'date_range': {
                            'start': '',
                            'end': ''
                        },
                        'bars_count': result.get('data_points', 0)
                    },
                    'data_quality': result.get('data_quality', {
                        'valid_bars': result.get('data_points', 0),
                        'invalid_bars': 0,
                        'duplicate_bars': 0,
                        'is_valid': True
                    }),
                    'cache_hit': result.get('cache_hit', False),
                    'data_source': result.get('data_source', 'unknown')
                }
                
                return jsonify(response)
                
            except Exception as e:
                logger.error(f"Error loading chart data for {instrument_id}: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/echarts-data/<instrument_id>')
        def get_echarts_data(instrument_id: str):
            """Return chart data optimized for ECharts format."""
            try:
                if not self.echarts_loader:
                    return jsonify({'error': 'ECharts loader not available'}), 500
                
                # Parse request parameters
                timeframe = request.args.get('timeframe', '1min')
                limit = request.args.get('limit', type=int)
                max_points = request.args.get('max_points', default=10000, type=int)
                sampling = request.args.get('sampling', 'none').lower()
                
                # Parse sampling strategy
                sampling_strategy = SamplingStrategy.NONE
                if sampling == 'uniform':
                    sampling_strategy = SamplingStrategy.UNIFORM
                elif sampling == 'adaptive':
                    sampling_strategy = SamplingStrategy.ADAPTIVE
                elif sampling == 'time_based':
                    sampling_strategy = SamplingStrategy.TIME_BASED
                    
                # Parse datetime parameters
                before_datetime = None
                after_datetime = None
                
                if request.args.get('before_timestamp_seconds'):
                    try:
                        before_timestamp = float(request.args.get('before_timestamp_seconds'))
                        before_datetime = datetime.fromtimestamp(before_timestamp)
                    except (ValueError, TypeError):
                        pass
                        
                if request.args.get('after_timestamp_seconds'):
                    try:
                        after_timestamp = float(request.args.get('after_timestamp_seconds'))
                        after_datetime = datetime.fromtimestamp(after_timestamp)
                    except (ValueError, TypeError):
                        pass
                
                # Create ECharts load request
                echarts_request = EChartsLoadRequest(
                    instrument=instrument_id,
                    timeframe=timeframe,
                    limit=limit,
                    before_datetime=before_datetime,
                    after_datetime=after_datetime,
                    sampling=sampling_strategy,
                    max_points=max_points,
                    enable_compression=True
                )
                
                # Load data using ECharts-optimized loader
                response = self.echarts_loader.load_data(echarts_request)
                
                # Convert EChartsResponse to dict for JSON serialization
                response_dict = {
                    'ohlc': response.ohlc,
                    'volume': response.volume,
                    'instrument': response.instrument,
                    'timeframe': response.timeframe,
                    'bars_returned': response.bars_returned,
                    'total_bars_available': response.total_bars_available,
                    'sampling_applied': response.sampling_applied.value,
                    'load_time_ms': response.load_time_ms,
                    'cache_hit': response.cache_hit,
                    'data_quality': response.data_quality,
                    'statistics': response.statistics,
                    'performance': {
                        'data_source': 'echarts_optimized',
                        'format': 'native_echarts',
                        'compression': 'enabled' if echarts_request.enable_compression else 'disabled'
                    }
                }
                
                logger.info(f"ECharts API: Served {response.bars_returned} bars for {instrument_id} in {response.load_time_ms:.1f}ms")
                return jsonify(response_dict)
                
            except Exception as e:
                logger.error(f"Error loading ECharts data for {instrument_id}: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/favicon.ico')
        def favicon():
            """Serve favicon."""
            from flask import send_from_directory
            return send_from_directory('static', 'favicon.ico')

        @self.app.route('/api/health')
        def health_check():
            """Health check endpoint."""
            try:
                health_status = self._health_check()
                health_status['websocket_enabled'] = True
                # Get active connections count with thread safety
                with self._subscriptions_lock:
                    health_status['active_connections'] = len(self._active_subscriptions)
                
                # Add pre-warming statistics
                prewarming_manager = get_prewarming_manager()
                if prewarming_manager:
                    prewarming_stats = prewarming_manager.get_prewarming_stats()
                    health_status['prewarming'] = prewarming_stats
                else:
                    health_status['prewarming'] = {'enabled': False, 'status': 'not_initialized'}
                
                return jsonify(health_status)
            except Exception as e:
                logger.error(f"Health check failed: {e}")
                return jsonify({'status': 'error', 'error': str(e)}), 500

    def _register_socket_events(self):
        """Register WebSocket event handlers."""

        @self.socketio.on('connect')
        def handle_connect():
            """Handle client connection with enterprise connection management."""
            client_info = {
                'connected_at': time.time(),
                'user_agent': request.headers.get('User-Agent', 'Unknown'),
                'origin': request.headers.get('Origin', 'Unknown')
            }
            
            # Add to connection manager
            self._connection_manager.add_connection(request.sid, client_info)
            
            logger.info(f"Client connected: {request.sid} (total: {self._connection_manager.get_connection_count()})")
            emit('connection_status', {'status': 'connected', 'sid': request.sid})

        @self.socketio.on('disconnect')
        def handle_disconnect():
            """Handle client disconnection with proper cleanup."""
            logger.info(f"Client disconnected: {request.sid}")

            # Remove from connection manager
            connection_data = self._connection_manager.remove_connection(request.sid)
            
            # Clean up subscriptions for this client with thread safety
            with self._subscriptions_lock:
                if request.sid in self._active_subscriptions:
                    del self._active_subscriptions[request.sid]
            
            if connection_data:
                duration = time.time() - connection_data['connection_time']
                logger.debug(f"Connection {request.sid} lasted {duration:.1f}s, "
                           f"{connection_data['message_count']} messages processed")

        @self.socketio.on('request_chart_data')
        def handle_chart_data_request(data):
            """Handle chart data request with connection activity tracking."""
            try:
                # Track connection activity
                self._connection_manager.update_connection_activity(request.sid)
                # Import validation at function level to avoid circular imports
                from .validation import validate_websocket_message
                
                # Validate WebSocket message
                is_valid, error_response, validated_data = validate_websocket_message(data)
                if not is_valid:
                    logger.warning(f"WebSocket validation error: {error_response}")
                    emit('chart_data', error_response)
                    return
                
                # Extract validated parameters
                instrument_id = validated_data['instrument_id']
                timeframe = validated_data['timeframe']
                limit = validated_data.get('limit', self.config.max_points)
                before_datetime = validated_data.get('before_datetime')
                after_datetime = validated_data.get('after_datetime')

                # Enhanced logging for infinite scroll debugging
                if before_datetime or after_datetime:
                    logger.info(f"🔄 INFINITE SCROLL REQUEST: {instrument_id}, {timeframe}, limit={limit}, before={before_datetime}, after={after_datetime}")
                else:
                    logger.info(f"Chart data requested: {instrument_id}, {timeframe}, limit={limit}")

                # Create request key for deduplication
                request_key = self._create_request_key(
                    instrument_id, 
                    timeframe,
                    limit=limit,
                    before_datetime=before_datetime,
                    after_datetime=after_datetime
                )

                # Use deduplication to prevent concurrent identical requests
                def load_data_sync():
                    # Try pre-warmed data first for ultra-fast WebSocket response
                    prewarming_manager = get_prewarming_manager()
                    response_data = None
                
                # Try smart cache first
                if prewarming_manager and prewarming_manager.is_prewarmed(instrument_id):
                    logger.info(f"⚡ WebSocket {instrument_id} trying smart cache")
                    response_data = prewarming_manager.get_prewarmed_data(
                        instrument=instrument_id,
                        before_datetime=before_datetime,
                        limit=limit
                    )
                
                # Use enhanced data loader if smart cache didn't serve the data
                if not response_data and self.data_loader:
                    if prewarming_manager and prewarming_manager.is_prewarmed(instrument_id):
                        logger.info(f"🔄 WebSocket smart cache miss - using enhanced loader for {instrument_id}")
                    else:
                        logger.info(f"🔄 WebSocket loading {instrument_id} (standard cache)")
                    response_data = self.data_loader.load_data(
                        instrument=instrument_id,
                        timeframe=timeframe,
                        limit=limit,
                        before_datetime=before_datetime,
                        after_datetime=after_datetime
                    )
                    
                    # Enhanced loader returns dict with ohlc, volume, and metadata
                    if not response_data.get('ohlc'):
                        emit('chart_data', {
                            'error': f'No data found for {instrument_id}',
                            'instrument': instrument_id,
                            'timeframe': timeframe
                        })
                        return
                    
                    # Add memory status information expected by frontend
                    memory_metrics = self.data_loader.get_performance_metrics()
                    
                    # Calculate actual date range from OHLCV data
                    start_date = 'N/A'
                    end_date = 'N/A'
                    
                    if response_data.get('ohlc') and len(response_data['ohlc']) > 0:
                        try:
                            # Get first and last timestamps from OHLCV data
                            first_time = response_data['ohlc'][0]['time']
                            last_time = response_data['ohlc'][-1]['time']
                            
                            # Convert from seconds to ISO format for frontend Date parsing
                            start_date = datetime.fromtimestamp(first_time).isoformat()
                            end_date = datetime.fromtimestamp(last_time).isoformat()
                        except (KeyError, IndexError, ValueError, TypeError) as e:
                            logger.warning(f"Error calculating date range for {instrument_id}: {e}")
                            start_date = 'N/A'
                            end_date = 'N/A'
                    
                    response = {
                        'ohlc': response_data['ohlc'],
                        'volume': response_data.get('volume', []),
                        'instrument': instrument_id,
                        'timeframe': timeframe,
                        'bars_returned': response_data.get('data_points', len(response_data['ohlc'])),
                        'memory_status': {
                            'memory_utilization': memory_metrics.get('memory_usage_mb', 0) / memory_metrics.get('max_memory_mb', 512) if memory_metrics.get('max_memory_mb', 0) > 0 else 0,
                            'data_points': response_data.get('data_points', len(response_data['ohlc'])),
                            'cache_hit': response_data.get('cache_hit', False)
                        },
                        'statistics': {
                            'date_range': {
                                'start': start_date,
                                'end': end_date
                            },
                            'bars_count': response_data.get('data_points', len(response_data['ohlc']))
                        },
                        'data_quality': {
                            'valid_bars': response_data.get('data_points', len(response_data['ohlc'])),
                            'invalid_bars': 0,
                            'duplicate_bars': 0,
                            'is_valid': True  # Enhanced loader validates data
                        }
                    }
                else:
                    # Fallback to legacy data loading
                    chart_data = load_instrument_data(
                        instrument_id=instrument_id,
                        timeframe=timeframe,
                        limit=limit
                    )

                    # Check if we have valid chart data (dict format)
                    if not isinstance(chart_data, dict) or not chart_data.get('ohlc'):
                        emit('chart_data', {
                            'error': f'No data found for {instrument_id}',
                            'instrument': instrument_id,
                            'timeframe': timeframe
                        })
                        return

                    # Chart data is already in expected format (dict)
                    response = {
                        'ohlc': chart_data.get('ohlc', []),
                        'volume': chart_data.get('volume', []),
                        'instrument': instrument_id,
                        'timeframe': timeframe,
                        'bars_returned': len(chart_data.get('ohlc', [])),
                        'cache_hit': chart_data.get('cache_hit', False),
                        'data_source': chart_data.get('data_source', 'legacy_fallback')
                    }
                
                # Add basic memory status for legacy loading
                response['memory_status'] = {
                    'memory_utilization': 0.5,  # Estimate for legacy loading
                    'data_points': response.get('bars_returned', 0),
                    'cache_hit': False
                }

                # Store subscription for updates with thread safety
                with self._subscriptions_lock:
                    self._active_subscriptions[request.sid] = {
                        'instrument_id': instrument_id,
                        'timeframe': timeframe,
                        'last_update': time.time()
                    }

                # Send data
                emit('chart_data', response)

            except Exception as e:
                logger.error(f"Error handling chart data request: {e}")
                emit('chart_data', {'error': str(e)})

        @self.socketio.on('subscribe_updates')
        def handle_subscribe_updates(data):
            """Handle subscription to real-time updates."""
            try:
                instrument_id = data.get('instrument_id')
                timeframe = data.get('timeframe', '1min')

                if not instrument_id:
                    emit('subscription_status', {'error': 'Missing instrument_id'})
                    return

                # Update subscription with thread safety
                with self._subscriptions_lock:
                    self._active_subscriptions[request.sid] = {
                        'instrument_id': instrument_id,
                        'timeframe': timeframe,
                        'last_update': time.time()
                    }

                emit('subscription_status', {
                    'status': 'subscribed',
                    'instrument': instrument_id,
                    'timeframe': timeframe
                })

                logger.info(f"Client {request.sid} subscribed to {instrument_id} updates")

            except Exception as e:
                logger.error(f"Error handling subscription: {e}")
                emit('subscription_status', {'error': str(e)})

        @self.socketio.on('unsubscribe_updates')
        def handle_unsubscribe_updates(data):
            """Handle unsubscription from updates."""
            try:
                with self._subscriptions_lock:
                    if request.sid in self._active_subscriptions:
                        del self._active_subscriptions[request.sid]
                emit('subscription_status', {'status': 'unsubscribed'})
                logger.info(f"Client {request.sid} unsubscribed from updates")

            except Exception as e:
                logger.error(f"Error handling unsubscription: {e}")

    def _create_request_key(self, instrument_id: str, timeframe: str, **kwargs) -> str:
        """
        Create a unique request key for deduplication.
        
        This method creates a deterministic key based on request parameters
        to identify identical requests and prevent concurrent processing.
        """
        # Sort kwargs for consistent key generation
        sorted_kwargs = dict(sorted(kwargs.items()))
        
        # Create request identifier
        request_data = {
            'instrument': instrument_id,
            'timeframe': timeframe,
            **sorted_kwargs
        }
        
        return f"{instrument_id}:{timeframe}:{hash(frozenset(sorted_kwargs.items()))}"
    
    def _deduplicate_data_request(self, request_key: str, request_func, *args, **kwargs):
        """
        Execute a data request with deduplication to prevent concurrent identical requests.
        
        This method implements enterprise-grade request deduplication following
        the patterns identified in the bug analysis for preventing race conditions.
        """
        with self._request_lock:
            # Check if we already have a pending request for this key
            if request_key in self._pending_data_requests:
                logger.debug(f"🔄 Deduplicating concurrent request: {request_key}")
                # Return the existing future
                return self._pending_data_requests[request_key]
            
            # Create new request future
            import concurrent.futures
            import threading
            
            # Use a thread pool executor for the actual request
            executor = concurrent.futures.ThreadPoolExecutor(max_workers=1)
            
            try:
                # Submit the request
                future = executor.submit(request_func, *args, **kwargs)
                
                # Store in pending requests
                self._pending_data_requests[request_key] = future
                
                # Set up cleanup when request completes
                def cleanup_request(fut):
                    with self._request_lock:
                        self._pending_data_requests.pop(request_key, None)
                    executor.shutdown(wait=False)
                
                future.add_done_callback(cleanup_request)
                
                logger.debug(f"📡 Created new data request: {request_key}")
                return future
                
            except Exception as e:
                # Cleanup on error
                self._pending_data_requests.pop(request_key, None)
                executor.shutdown(wait=False)
                raise e

    def _start_update_thread(self):
        """Start the background thread for sending updates."""
        if self._update_thread is None or not self._update_thread.is_alive():
            self._stop_updates = False
            self._update_thread = threading.Thread(target=self._update_loop, daemon=True)
            self._update_thread.start()
            logger.info("Update thread started")

    def _stop_update_thread(self):
        """Stop the background update thread."""
        self._stop_updates = True
        if self._update_thread and self._update_thread.is_alive():
            self._update_thread.join(timeout=5)
            logger.info("Update thread stopped")

    def _update_loop(self):
        """Background loop for sending real-time updates."""
        while not self._stop_updates:
            try:
                # Check if we have active subscriptions with thread safety
                with self._subscriptions_lock:
                    has_subscriptions = bool(self._active_subscriptions)
                
                if has_subscriptions:
                    self._send_updates()

                # Sleep for update interval (e.g., every 5 seconds)
                time.sleep(5)

            except Exception as e:
                logger.error(f"Error in update loop: {e}")
                time.sleep(1)

    def _send_updates(self):
        """Send updates to subscribed clients using enterprise connection management."""
        current_time = time.time()

        # Create a thread-safe copy of subscriptions
        with self._subscriptions_lock:
            subscriptions_copy = dict(self._active_subscriptions)

        # Use connection manager to validate active connections
        active_connections = self._connection_manager.get_all_connections()

        for sid, subscription in subscriptions_copy.items():
            try:
                # Check if connection is still managed (more reliable than SocketIO check)
                if sid not in active_connections:
                    with self._subscriptions_lock:
                        self._active_subscriptions.pop(sid, None)
                    continue
                
                # Update connection activity
                self._connection_manager.update_connection_activity(sid)

                # Generate simulated update (in real implementation, this would
                # fetch actual new data)
                update_data = self._generate_simulated_update(subscription)

                if update_data:
                    self.socketio.emit('chart_update', update_data, room=sid)
                    # Update the timestamp in the original dictionary with thread safety
                    with self._subscriptions_lock:
                        if sid in self._active_subscriptions:
                            self._active_subscriptions[sid]['last_update'] = current_time

            except Exception as e:
                logger.error(f"Error sending update to {sid}: {e}")
                # Remove problematic subscription with thread safety
                with self._subscriptions_lock:
                    self._active_subscriptions.pop(sid, None)

    def _generate_simulated_update(self, subscription: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Generate simulated real-time update.

        In a real implementation, this would fetch actual new data from the data source.
        """
        try:
            # For demo purposes, generate a random price update
            # In production, this would fetch real new data

            current_time = int(time.time())  # Unix timestamp in seconds for TradingView charts

            # Simulate price movement
            base_price = 100.0 + random.uniform(-10, 10)
            price_change = random.uniform(-0.5, 0.5)

            new_price = base_price + price_change
            volume = random.randint(1000, 10000)

            # Create OHLC bar (simplified for demo)
            ohlc_update = {
                'time': current_time,
                'open': base_price,
                'high': max(base_price, new_price) + random.uniform(0, 0.1),
                'low': min(base_price, new_price) - random.uniform(0, 0.1),
                'close': new_price
            }

            volume_update = {
                'time': current_time,
                'value': volume,
                'color': '#26a69a' if new_price > base_price else '#ef5350'
            }

            return {
                'ohlc': ohlc_update,
                'volume': volume_update,
                'instrument': subscription['instrument_id'],
                'timeframe': subscription['timeframe']
            }

        except Exception as e:
            logger.error(f"Error generating simulated update: {e}")
            return None

    def start_server(self) -> None:
        """Start the WebSocket server."""
        try:
            # Validate configuration
            if not self.config.validate():
                raise ValueError("Invalid configuration")

            # Create test data if requested
            if self.config.enable_test_data:
                self._create_test_data()

            # Start update thread
            self._start_update_thread()
            
            # Start pre-warming in background for ultra-smooth user experience
            if self._prewarming_config.get('enable_prewarming', True):
                logger.info("🚀 Starting background pre-warming for <60ms response times...")
                self._prewarming_thread = start_prewarming_async(self._prewarming_config)
            else:
                logger.info("🔄 Pre-warming disabled - using standard response times")

            # Log startup information
            logger.info(f"Starting Nautilus Trader WebSocket Chart Server")
            logger.info(f"Server URL: {self.config.get_server_url()}")
            logger.info(f"WebSocket enabled: True")
            logger.info(f"Catalog path: {self.config.catalog_path}")
            logger.info(f"Debug mode: {self.config.debug}")
            logger.info(f"Pre-warming enabled: {self._prewarming_config.get('enable_prewarming', True)}")

            # Start SocketIO server
            self.socketio.run(
                self.app,
                host=self.config.host,
                port=self.config.port,
                debug=self.config.debug,
                use_reloader=False  # Disable reloader to prevent issues with threading
            )

        except Exception as e:
            logger.error(f"Error starting WebSocket server: {e}")
            raise
        finally:
            self._stop_update_thread()

    def stop_server(self) -> None:
        """Stop the WebSocket server."""
        self._stop_update_thread()
        logger.info("WebSocket server stop requested")

    def get_available_instruments(self) -> list:
        """Get available instruments from the data loader."""
        return get_available_instruments()

    def validate_data_source(self, path: str) -> bool:
        """Validate that the data source is accessible."""
        try:
            if not os.path.exists(path):
                return False

            # Check if it's a directory with the expected structure
            data_dir = os.path.join(path, "data", "bar")
            if not os.path.exists(data_dir):
                return False

            # Check if there are any instrument directories
            try:
                subdirs = [d for d in os.listdir(data_dir)
                          if os.path.isdir(os.path.join(data_dir, d))]
                return len(subdirs) > 0
            except OSError:
                return False

        except Exception as e:
            logger.error(f"Error validating data source {path}: {e}")
            return False

    def _create_test_data(self):
        """Create test data if it doesn't exist."""
        try:
            if not os.path.exists(self.config.catalog_path):
                logger.info("Creating test catalog with sample data")

                symbols = self.config.test_symbols.split(',')
                success = create_test_catalog(
                    catalog_path=self.config.catalog_path,
                    symbols=symbols,
                    bars_per_symbol=self.config.test_bars
                )

                if success:
                    logger.info(f"Test catalog created at {self.config.catalog_path}")
                else:
                    logger.error("Failed to create test catalog")
            else:
                logger.info(f"Using existing catalog at {self.config.catalog_path}")

        except Exception as e:
            logger.error(f"Error creating test data: {e}")

    def _health_check(self) -> dict:
        """Perform health check."""
        try:
            # Check if catalog path exists and is accessible
            catalog_accessible = self.validate_data_source(self.config.catalog_path)
            
            # Get available instruments count
            instruments = get_available_instruments()
            instrument_count = len(instruments)
            
            # Include connection manager metrics
            connection_metrics = {
                'total_connections': self._connection_manager.get_connection_count() if self._connection_manager else 0,
                'active_subscriptions': len(self._active_subscriptions)
            }
            
            return {
                'status': 'healthy' if catalog_accessible else 'unhealthy',
                'catalog_path': self.config.catalog_path,
                'catalog_accessible': catalog_accessible,
                'instrument_count': instrument_count,
                'server_url': self.config.get_server_url(),
                'connection_metrics': connection_metrics
            }
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }

    def shutdown(self):
        """
        Gracefully shutdown the WebSocket server with proper cleanup.
        
        This method ensures all resources are properly cleaned up,
        following enterprise shutdown best practices.
        """
        logger.info("Initiating WebSocket server shutdown...")
        
        try:
            # Stop update thread
            self._stop_update_thread()
            
            # Stop connection manager health monitoring
            if self._connection_manager:
                self._connection_manager.stop_health_monitoring()
                logger.info("Connection manager shutdown completed")
            
            # Stop prewarming if active
            if self._prewarming_thread and self._prewarming_thread.is_alive():
                # Note: PreWarmingManager should handle its own cleanup
                logger.info("Prewarming thread cleanup initiated")
            
            # Clear active subscriptions
            with self._subscriptions_lock:
                subscription_count = len(self._active_subscriptions)
                self._active_subscriptions.clear()
                if subscription_count > 0:
                    logger.info(f"Cleared {subscription_count} active subscriptions")
            
            # Clear pending data requests to prevent memory leaks
            with self._request_lock:
                pending_count = len(self._pending_data_requests)
                for request_key, future in self._pending_data_requests.items():
                    try:
                        if not future.done():
                            future.cancel()
                    except Exception as e:
                        logger.debug(f"Error cancelling pending request {request_key}: {e}")
                self._pending_data_requests.clear()
                if pending_count > 0:
                    logger.info(f"Cancelled {pending_count} pending data requests")
            
            logger.info("WebSocket server shutdown completed successfully")
            
        except Exception as e:
            logger.error(f"Error during WebSocket server shutdown: {e}")
            raise


def create_websocket_app(config: Optional[ChartConfig] = None) -> Flask:
    """
    Create and configure WebSocket Flask application.

    Args:
        config: Chart configuration (optional)

    Returns:
        Configured Flask application with SocketIO
    """
    if config is None:
        config = ChartConfig()

    server = WebSocketChartServer(config)
    return server.app


def run_websocket_server(
    host: str = "0.0.0.0",
    port: int = 8081,
    catalog_path: str = "/tmp/test_catalog",
    debug: bool = False,
    log_level: str = "INFO",
    **kwargs
) -> None:
    """
    Run the WebSocket chart server with specified configuration.

    Args:
        host: Host to bind to
        port: Port to listen on
        catalog_path: Path to the data catalog
        debug: Enable debug mode
        log_level: Logging level
        **kwargs: Additional configuration options
    """
    try:
        # Create configuration
        config_dict = {
            'host': host,
            'port': port,
            'catalog_path': catalog_path,
            'debug': debug,
            'log_level': log_level,
            'enable_websocket': True,
            **kwargs
        }

        config = ChartConfig(**config_dict)

        # Create and start server
        server = WebSocketChartServer(config)
        server.start_server()

    except KeyboardInterrupt:
        logger.info("WebSocket server stopped by user")
    except Exception as e:
        logger.error(f"WebSocket server error: {e}")
        raise


if __name__ == "__main__":
    # Basic command line interface
    import argparse

    parser = argparse.ArgumentParser(description="Nautilus Trader WebSocket Chart Server")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8081, help="Port to listen on")
    parser.add_argument("--catalog", default="/tmp/test_catalog", help="Catalog path")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    parser.add_argument("--log-level", default="INFO", help="Logging level")
    parser.add_argument("--test-data", action="store_true", help="Create test data")

    args = parser.parse_args()

    run_websocket_server(
        host=args.host,
        port=args.port,
        catalog_path=args.catalog,
        debug=args.debug,
        log_level=args.log_level,
        enable_test_data=args.test_data
    )
