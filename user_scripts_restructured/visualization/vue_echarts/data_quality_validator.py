#!/usr/bin/env python3
"""
Data Quality Validator - Collecting Parameter Pattern Implementation

Follows Java design patterns research to implement comprehensive data validation
using the collecting parameter pattern for accumulating validation results.
This replaces placeholder validation with actual quality checks.

Based on research from Java design patterns and validation best practices.
"""

import logging
import time as time_module
from typing import Dict, List, Any, Tuple, Optional, Set
from dataclasses import dataclass, field
from datetime import datetime
from collections import defaultdict

logger = logging.getLogger(__name__)

@dataclass
class ValidationIssue:
    """Represents a single validation issue (follows notification pattern)."""
    code: str
    message: str
    field: Optional[str] = None
    value: Optional[Any] = None
    severity: str = 'error'  # 'error', 'warning', 'info'
    timestamp: float = 0.0
    bar_index: Optional[int] = None

@dataclass
class ValidationContext:
    """Validation execution context."""
    operation: str
    instrument: str
    started_at: float = 0.0
    completed_at: Optional[float] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def complete(self) -> float:
        """Mark validation as completed and return duration."""
        self.completed_at = time_module.time()
        return self.completed_at - self.started_at

@dataclass
class DataQualityMetrics:
    """Comprehensive data quality metrics."""
    total_bars: int = 0
    valid_bars: int = 0
    invalid_bars: int = 0
    duplicate_bars: int = 0
    completeness_percentage: float = 0.0
    continuity_gaps: int = 0
    quality_score: float = 0.0
    
    # OHLC-specific metrics
    ohlc_relationship_violations: int = 0
    extreme_price_movements: int = 0
    zero_volume_bars: int = 0
    negative_values: int = 0
    
    # Timestamp metrics
    timestamp_gaps: int = 0
    out_of_order_timestamps: int = 0
    duplicate_timestamps: int = 0
    
    # Statistical metrics
    price_range_violations: int = 0
    volume_anomalies: int = 0

class DataQualityCollector:
    """
    Collecting Parameter implementation for data quality validation.
    
    This class follows the collecting parameter pattern from Java design patterns
    research, accumulating validation results during the validation process.
    """
    
    def __init__(self, context: ValidationContext):
        self.context = context
        self.errors: List[ValidationIssue] = []
        self.warnings: List[ValidationIssue] = []
        self.info: List[ValidationIssue] = []
        self.metrics = DataQualityMetrics()
        
        # Internal state for validation
        self._seen_timestamps: Set[int] = set()
        self._previous_timestamp: Optional[int] = None
        self._price_history: List[float] = []
    
    def add_error(self, code: str, message: str, **kwargs) -> None:
        """Add a validation error (blocking issue)."""
        issue = ValidationIssue(
            code=code,
            message=message,
            severity='error',
            **kwargs
        )
        self.errors.append(issue)
        logger.debug(f"Validation error [{code}]: {message}")
    
    def add_warning(self, code: str, message: str, **kwargs) -> None:
        """Add a validation warning (non-blocking issue)."""
        issue = ValidationIssue(
            code=code,
            message=message,
            severity='warning',
            **kwargs
        )
        self.warnings.append(issue)
        logger.debug(f"Validation warning [{code}]: {message}")
    
    def add_info(self, code: str, message: str, **kwargs) -> None:
        """Add a validation info item."""
        issue = ValidationIssue(
            code=code,
            message=message,
            severity='info',
            **kwargs
        )
        self.info.append(issue)
    
    def validate_ohlc_bar(self, bar: Dict[str, Any], bar_index: int) -> bool:
        """Validate a single OHLC bar and collect issues."""
        is_valid = True
        
        try:
            # Extract values
            open_price = bar.get('open')
            high_price = bar.get('high')
            low_price = bar.get('low')
            close_price = bar.get('close')
            volume = bar.get('volume', 0)
            timestamp = bar.get('time')
            
            # Validate required fields
            if any(val is None for val in [open_price, high_price, low_price, close_price]):
                self.add_error(
                    'MISSING_OHLC_VALUES',
                    f"Missing required OHLC values in bar {bar_index}",
                    bar_index=bar_index,
                    value=bar
                )
                self.metrics.invalid_bars += 1
                return False
            
            if timestamp is None:
                self.add_error(
                    'MISSING_TIMESTAMP',
                    f"Missing timestamp in bar {bar_index}",
                    bar_index=bar_index
                )
                self.metrics.invalid_bars += 1
                return False
            
            # Validate numeric types and positive values
            prices = [open_price, high_price, low_price, close_price]
            for i, price in enumerate(prices):
                price_names = ['open', 'high', 'low', 'close']
                if not isinstance(price, (int, float)):
                    self.add_error(
                        'INVALID_PRICE_TYPE',
                        f"Invalid {price_names[i]} price type in bar {bar_index}: {type(price)}",
                        field=price_names[i],
                        value=price,
                        bar_index=bar_index
                    )
                    is_valid = False
                elif price <= 0:
                    self.add_error(
                        'NEGATIVE_PRICE',
                        f"Negative or zero {price_names[i]} price in bar {bar_index}: {price}",
                        field=price_names[i],
                        value=price,
                        bar_index=bar_index
                    )
                    self.metrics.negative_values += 1
                    is_valid = False
            
            if not is_valid:
                self.metrics.invalid_bars += 1
                return False
            
            # Validate OHLC relationships
            if high_price < max(open_price, close_price):
                self.add_warning(
                    'HIGH_BELOW_OC',
                    f"High ({high_price}) below open/close in bar {bar_index}",
                    bar_index=bar_index
                )
                self.metrics.ohlc_relationship_violations += 1
            
            if low_price > min(open_price, close_price):
                self.add_warning(
                    'LOW_ABOVE_OC',
                    f"Low ({low_price}) above open/close in bar {bar_index}",
                    bar_index=bar_index
                )
                self.metrics.ohlc_relationship_violations += 1
            
            if high_price < low_price:
                self.add_error(
                    'HIGH_BELOW_LOW',
                    f"High ({high_price}) below low ({low_price}) in bar {bar_index}",
                    bar_index=bar_index
                )
                self.metrics.ohlc_relationship_violations += 1
                is_valid = False
            
            # Validate volume
            if volume is not None:
                if not isinstance(volume, (int, float)):
                    self.add_warning(
                        'INVALID_VOLUME_TYPE',
                        f"Invalid volume type in bar {bar_index}: {type(volume)}",
                        field='volume',
                        value=volume,
                        bar_index=bar_index
                    )
                elif volume < 0:
                    self.add_error(
                        'NEGATIVE_VOLUME',
                        f"Negative volume in bar {bar_index}: {volume}",
                        field='volume',
                        value=volume,
                        bar_index=bar_index
                    )
                    is_valid = False
                elif volume == 0:
                    self.metrics.zero_volume_bars += 1
            
            # Validate timestamp
            timestamp_int = int(timestamp)
            
            # Check for duplicate timestamps
            if timestamp_int in self._seen_timestamps:
                self.add_warning(
                    'DUPLICATE_TIMESTAMP',
                    f"Duplicate timestamp in bar {bar_index}: {timestamp_int}",
                    bar_index=bar_index,
                    value=timestamp_int
                )
                self.metrics.duplicate_timestamps += 1
                self.metrics.duplicate_bars += 1
            else:
                self._seen_timestamps.add(timestamp_int)
            
            # Check timestamp ordering
            if self._previous_timestamp is not None:
                if timestamp_int <= self._previous_timestamp:
                    self.add_warning(
                        'OUT_OF_ORDER_TIMESTAMP',
                        f"Out of order timestamp in bar {bar_index}: {timestamp_int} <= {self._previous_timestamp}",
                        bar_index=bar_index,
                        value=timestamp_int
                    )
                    self.metrics.out_of_order_timestamps += 1
                elif timestamp_int - self._previous_timestamp > 86400 * 7:  # More than 1 week gap
                    self.add_info(
                        'LARGE_TIMESTAMP_GAP',
                        f"Large timestamp gap in bar {bar_index}: {timestamp_int - self._previous_timestamp} seconds",
                        bar_index=bar_index
                    )
                    self.metrics.timestamp_gaps += 1
            
            self._previous_timestamp = timestamp_int
            
            # Check for extreme price movements
            if self._price_history:
                last_close = self._price_history[-1]
                price_change = abs(close_price - last_close) / last_close
                if price_change > 0.5:  # 50% movement
                    self.add_warning(
                        'EXTREME_PRICE_MOVEMENT',
                        f"Extreme price movement in bar {bar_index}: {price_change:.2%}",
                        bar_index=bar_index,
                        value=price_change
                    )
                    self.metrics.extreme_price_movements += 1
            
            self._price_history.append(close_price)
            if len(self._price_history) > 100:  # Keep only recent history
                self._price_history.pop(0)
            
            if is_valid:
                self.metrics.valid_bars += 1
            else:
                self.metrics.invalid_bars += 1
            
            return is_valid
            
        except Exception as e:
            self.add_error(
                'VALIDATION_EXCEPTION',
                f"Exception validating bar {bar_index}: {str(e)}",
                bar_index=bar_index,
                value=str(e)
            )
            self.metrics.invalid_bars += 1
            return False
    
    def validate_dataset_continuity(self, ohlc_data: List[Dict[str, Any]]) -> None:
        """Validate overall dataset continuity and quality."""
        if not ohlc_data:
            self.add_error('EMPTY_DATASET', 'Dataset is empty')
            return
        
        # Sort by timestamp for continuity checking
        try:
            sorted_data = sorted(ohlc_data, key=lambda x: x.get('time', 0))
            
            # Check for significant gaps in time series
            if len(sorted_data) > 1:
                timestamps = [bar.get('time', 0) for bar in sorted_data]
                gaps = []
                
                for i in range(1, len(timestamps)):
                    gap = timestamps[i] - timestamps[i-1]
                    if gap > 86400:  # More than 1 day
                        gaps.append(gap)
                
                if gaps:
                    self.metrics.continuity_gaps = len(gaps)
                    avg_gap = sum(gaps) / len(gaps)
                    self.add_info(
                        'CONTINUITY_GAPS',
                        f"Found {len(gaps)} large time gaps, average: {avg_gap/86400:.1f} days"
                    )
        
        except Exception as e:
            self.add_warning(
                'CONTINUITY_CHECK_FAILED',
                f"Failed to check dataset continuity: {str(e)}"
            )
    
    def finalize_metrics(self) -> None:
        """Calculate final quality metrics."""
        self.metrics.total_bars = self.metrics.valid_bars + self.metrics.invalid_bars
        
        if self.metrics.total_bars > 0:
            self.metrics.completeness_percentage = (
                self.metrics.valid_bars / self.metrics.total_bars
            ) * 100
        
        # Calculate quality score (0-100)
        score = 100.0
        
        # Deduct for invalid data
        if self.metrics.total_bars > 0:
            invalid_ratio = self.metrics.invalid_bars / self.metrics.total_bars
            score -= invalid_ratio * 50  # Up to 50 points for invalid data
        
        # Deduct for various issues
        score -= len(self.errors) * 5  # 5 points per error
        score -= len(self.warnings) * 2  # 2 points per warning
        score -= self.metrics.ohlc_relationship_violations * 0.1
        score -= self.metrics.duplicate_bars * 1
        score -= self.metrics.extreme_price_movements * 0.5
        
        self.metrics.quality_score = max(0.0, min(100.0, score))
    
    def get_result(self) -> Dict[str, Any]:
        """Get final validation result in official data quality format."""
        self.finalize_metrics()
        duration = self.context.complete()
        
        # Determine overall validity
        is_valid = (
            len(self.errors) == 0 and 
            self.metrics.completeness_percentage >= 80 and
            self.metrics.quality_score >= 70
        )
        
        # Collect issues and warnings
        issues = [issue.message for issue in self.errors] if self.errors else None
        warnings = [issue.message for issue in self.warnings] if self.warnings else None
        
        return {
            'is_valid': is_valid,
            'issues': issues,
            'warnings': warnings,
            'data_source': 'smart_cache_segmentation',
            'metrics': {
                'total_bars': self.metrics.total_bars,
                'valid_bars': self.metrics.valid_bars,
                'invalid_bars': self.metrics.invalid_bars,
                'duplicate_bars': self.metrics.duplicate_bars,
                'completeness_percentage': self.metrics.completeness_percentage,
                'continuity_gaps': self.metrics.continuity_gaps,
                'quality_score': self.metrics.quality_score,
                'ohlc_relationship_violations': self.metrics.ohlc_relationship_violations,
                'extreme_price_movements': self.metrics.extreme_price_movements,
                'zero_volume_bars': self.metrics.zero_volume_bars,
                'timestamp_issues': {
                    'gaps': self.metrics.timestamp_gaps,
                    'out_of_order': self.metrics.out_of_order_timestamps,
                    'duplicates': self.metrics.duplicate_timestamps
                }
            },
            'validation_context': {
                'operation': self.context.operation,
                'instrument': self.context.instrument,
                'duration_ms': duration * 1000,
                'started_at': self.context.started_at,
                'completed_at': self.context.completed_at
            }
        }

class DataQualityValidator:
    """
    Main validator class implementing comprehensive data quality validation.
    
    Uses the collecting parameter pattern to accumulate validation results
    while processing data. Provides both quick validation and comprehensive
    analysis modes.
    """
    
    def __init__(self, enable_comprehensive_validation: bool = True):
        self.enable_comprehensive_validation = enable_comprehensive_validation
        self.validation_history: List[Dict[str, Any]] = []
    
    def validate_ohlc_data(
        self, 
        ohlc_data: List[Dict[str, Any]], 
        instrument: str,
        operation: str = 'data_validation'
    ) -> Dict[str, Any]:
        """
        Validate OHLC data using collecting parameter pattern.
        
        Args:
            ohlc_data: List of OHLC bar dictionaries
            instrument: Instrument identifier for context
            operation: Operation description for context
            
        Returns:
            Data quality result following official schema
        """
        # Create validation context
        context = ValidationContext(
            operation=operation,
            instrument=instrument,
            metadata={
                'input_bar_count': len(ohlc_data),
                'comprehensive_validation': self.enable_comprehensive_validation
            }
        )
        
        # Create collector (collecting parameter)
        collector = DataQualityCollector(context)
        
        # Quick validation for empty data
        if not ohlc_data:
            collector.add_error('EMPTY_DATASET', 'No OHLC data provided')
            result = collector.get_result()
            self.validation_history.append(result)
            return result
        
        # Validate each bar
        logger.debug(f"Validating {len(ohlc_data)} OHLC bars for {instrument}")
        
        for bar_index, bar in enumerate(ohlc_data):
            collector.validate_ohlc_bar(bar, bar_index)
            
            # Early exit for performance if not comprehensive
            if not self.enable_comprehensive_validation and len(collector.errors) > 10:
                collector.add_info(
                    'EARLY_EXIT',
                    f"Early exit after {bar_index + 1} bars due to excessive errors"
                )
                break
        
        # Validate dataset-level properties
        if self.enable_comprehensive_validation:
            collector.validate_dataset_continuity(ohlc_data)
        
        # Get final result
        result = collector.get_result()
        self.validation_history.append(result)
        
        logger.info(
            f"Validation completed for {instrument}: "
            f"valid={result['metrics']['valid_bars']}, "
            f"invalid={result['metrics']['invalid_bars']}, "
            f"quality_score={result['metrics']['quality_score']:.1f}"
        )
        
        return result
    
    def quick_validate(self, ohlc_data: List[Dict[str, Any]], instrument: str) -> Dict[str, Any]:
        """
        Quick validation for performance-critical paths.
        
        Returns minimal data quality information with basic checks only.
        """
        if not ohlc_data:
            return {
                'is_valid': False,
                'issues': ['Empty dataset'],
                'data_source': 'quick_validation'
            }
        
        # Basic checks only
        valid_bars = 0
        issues = []
        
        for i, bar in enumerate(ohlc_data[:100]):  # Check first 100 bars only
            if all(bar.get(field) is not None and bar.get(field) > 0 
                   for field in ['open', 'high', 'low', 'close']):
                valid_bars += 1
            elif len(issues) < 5:  # Limit issue collection
                issues.append(f"Invalid bar at index {i}")
        
        is_valid = valid_bars > 0 and len(issues) == 0
        quality_score = (valid_bars / min(len(ohlc_data), 100)) * 100
        
        return {
            'is_valid': is_valid,
            'issues': issues if issues else None,
            'data_source': 'quick_validation',
            'metrics': {
                'valid_bars': valid_bars,
                'invalid_bars': min(len(ohlc_data), 100) - valid_bars,
                'duplicate_bars': 0,  # Not checked in quick mode
                'quality_score': quality_score,
                'total_bars': len(ohlc_data)
            }
        }
    
    def get_validation_summary(self) -> Dict[str, Any]:
        """Get summary of recent validation operations."""
        if not self.validation_history:
            return {'total_validations': 0}
        
        recent = self.validation_history[-10:]  # Last 10 validations
        
        return {
            'total_validations': len(self.validation_history),
            'recent_validations': len(recent),
            'average_quality_score': sum(v['metrics']['quality_score'] for v in recent) / len(recent),
            'success_rate': sum(1 for v in recent if v['is_valid']) / len(recent) * 100,
            'common_issues': self._get_common_issues(recent)
        }
    
    def _get_common_issues(self, validations: List[Dict[str, Any]]) -> Dict[str, int]:
        """Extract common issues from validation history."""
        issue_counts = defaultdict(int)
        
        for validation in validations:
            if validation.get('issues'):
                for issue in validation['issues']:
                    # Extract issue type from message
                    issue_type = issue.split(':')[0] if ':' in issue else issue.split()[0]
                    issue_counts[issue_type] += 1
        
        return dict(issue_counts)

# Global validator instance for module use
_global_validator = DataQualityValidator(enable_comprehensive_validation=True)

def validate_ohlc_data(ohlc_data: List[Dict[str, Any]], instrument: str, **kwargs) -> Dict[str, Any]:
    """Convenience function for OHLC data validation."""
    return _global_validator.validate_ohlc_data(ohlc_data, instrument, **kwargs)

def quick_validate_ohlc_data(ohlc_data: List[Dict[str, Any]], instrument: str) -> Dict[str, Any]:
    """Convenience function for quick OHLC data validation."""
    return _global_validator.quick_validate(ohlc_data, instrument)
